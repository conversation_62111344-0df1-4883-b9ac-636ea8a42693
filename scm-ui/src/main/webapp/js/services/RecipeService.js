/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 02-07-2016.
 */
angular.module('scmApp').service('recipeService', ['$rootScope', 'appUtil', 'apiJson', '$http', 'metaDataService', '$toastService',
    function ($rootScope, appUtil, apiJson, $http, metaDataService, $toastService) {

    var service = {};

    service.productList = [];
    service.menuCategories = [];
    service.unitProductPackagingMap = [];
    service.getProductsFromCategoryList = getProductsFromCategoryList;
    service.getProductsFromCategoryListRegularOrdering=getProductsFromCategoryListRegularOrdering;
    service.getProductsFromRecipe = getProductsFromRecipe;
    service.createMenuItemCategories = createMenuItemCategories;
    service.setMOQToProducts = setMOQToProducts;
    service.getUnitProductPackagingMappings = getUnitProductPackagingMappings;

    service.getScmProductsFromDayWiseMenu = getScmProductsFromDayWiseMenu;
    service.getProductsFromCategoryListNewRegularOrdering = getProductsFromCategoryListNewRegularOrdering;

    function createMenuItemCategories() {
        service.menuCategories = [];
        service.productList = [];
        appUtil.getMenuProductCategories().forEach(function (item) {
            if (item.detail.id != 8) {   // check for combo products
                service.menuCategories.push({
                    id: item.detail.id,
                    name: item.detail.name,
                    productList: []
                });
            }
        });
        fillProductToCategories();
        return service.menuCategories;
    }

    function fillProductToCategories() {
        appUtil.getUnitData().products.forEach(function (product) {
            if(product.taxCode != null){
        	product.prices.forEach(function (price) {
                service.menuCategories.forEach(function (cat) {
                    	if (product.type === cat.id && product.classification != "FREE_ADDON" && product.status=="ACTIVE") {
                        cat.productList.push({
                            productId: product.id,
                            productName: product.name,
                            dimension: price.dimension,
                            quantity: null,
                            supportsVariantLevelOrdering: product.supportsVariantLevelOrdering,
                            variants: getVariants(product, price.dimension),
                            brandId: product.brandId
                        });
                    	}
                });
            	})
            }
        });
    }

    function getVariants(product, dimension) {
        var variants = [];
        //if (product.supportsVariantLevelOrdering) {
            product.prices.forEach(function (price) {
                if (price.recipe != null && price.dimension==dimension) {
                    price.recipe.ingredient.variants.forEach(function (variant) {
                        if (variant.status == "ACTIVE") {
                            if(variant.product.variantLevelOrdering){
                                variant.details.forEach(function (detail) {
                                    if (detail.status == "ACTIVE") {
                                        variants.push({
                                            id: null,
                                            name: detail.alias,
                                            conversionQuantity: detail.quantity,
                                            isDefault: detail.defaultSetting,
                                            orderedQuantity: null,
                                            dimension: price.dimension,
                                            variantLevelOrdering: variant.product.variantLevelOrdering
                                        })
                                    }
                                });
                            }else{
                                variant.details.forEach(function (detail) {
                                    if (detail.status == "ACTIVE" && detail.defaultSetting) {
                                        variants.push({
                                            id: null,
                                            name: detail.alias,
                                            conversionQuantity: detail.quantity,
                                            isDefault: detail.defaultSetting,
                                            orderedQuantity: null,
                                            dimension: price.dimension,
                                            variantLevelOrdering: variant.product.variantLevelOrdering
                                        })
                                    }
                                });
                            }
                        }
                    })
                }
            });
        //}
        return variants;
    }

        function getProductsFromCategoryList(unit, categoryList, callBack) {
            console.log('Calling getProductsFromCategoryList');
            var products = [];
            service.productList = [];
            categoryList.forEach(function (cat) {
                if (products.length == 0) {
                    products = cat.productList;
                } else {
                    products = products.concat(cat.productList);
                }
            });
            service.getProductsFromRecipe(unit,products);
            console.log('Calling stock');
            var data = {
                unitId : appUtil.getUnitData().id,
                scmProductList : service.productList
            };
            console.log('Product List ',data);
            $http({
                method: "POST",
                url: apiJson.urls.stockManagement.getCurrentStock,
                data: data
            }).then(function success(response) {
                console.log('Received Response for stock call',response);
                if (response.data != null) {
                    service.productList = response.data.scmProductList;
                    service.productList.forEach(function(item) {
                        if(item.stockAtHand == null || isNaN(item.stockAtHand)){
                            item.stockAtHand = 0;
                        }
                        if(item.saleQuantity == null || isNaN(item.saleQuantity)){
                            item.saleQuantity = 0;
                        }
                        if((item.stockAtHand - item.saleQuantity) > 0){
                            item.suggestedQuantity = item.suggestedQuantity - (item.stockAtHand - item.saleQuantity);
                            if(item.suggestedQuantity < 0){
                                item.suggestedQuantity = 0;
                            }
                        }
                        item.orderingQuantity = item.suggestedQuantity;
                        if (item.unitOfMeasure == 'PC' || item.unitOfMeasure == 'SACHET') {
                            item.orderingQuantity = Math.ceil(item.orderingQuantity);
                        } else {
                            item.orderingQuantity = parseFloat(item.orderingQuantity).toFixed(6)
                        }
                    });
                    service.setMOQToProducts();
                    if(!angular.isUndefined(callBack) && typeof callBack == "function"){
                        service.productList = callBack(service.productList);
                    }
                } else {
                    $toastService.create("Something went wrong. Please try again!");
                }
            }, function error(response) {
                $toastService.create("Something went wrong. Please try again!");
            });

        }

        function getScmProductsFromDayWiseMenu(unit, categoryList, callBack) {
            console.log('Calling getProductsFromCategoryListRegularOrdering');
            var products = [];
            service.productList = [];
            // service.getUnitProductPackagingMappings();
            categoryList.forEach(function (cat) {
                if (products.length == 0) {
                    products = cat.productList;
                } else {
                    products = products.concat(cat.productList);
                }
            });
            service.getProductsFromRecipe(unit, products);
            if (callBack != undefined && callBack != null) {
                callBack(service.productList);
            }
        }

    function getProductsFromCategoryListRegularOrdering(unit, categoryList,expiryProduct, callBack) {
    	console.log('Calling getProductsFromCategoryListRegularOrdering');
        var products = [];
        service.productList = [];
        service.getUnitProductPackagingMappings();
        categoryList.forEach(function (cat) {
            if (products.length == 0) {
                products = cat.productList;
            } else {
                products = products.concat(cat.productList);
            }
        });
        service.getProductsFromRecipe(unit,products);
        console.log('Calling stock');
        var data = {
        		unitId : appUtil.getUnitData().id,
        		scmProductList : service.productList
        };
        console.log('Product List ',data);
        $http({
            method: "POST",
            // url: apiJson.urls.stockManagement.getCurrentStock,
            url: apiJson.urls.stockManagement.getInTransitStock,
            // url: apiJson.urls.stockManagement.getLiveStock,
            params: {
                "isF9" : false
            },
            data: data
        }).then(function success(response) {
            console.log('Received Response for stock call',response);
            if (response.data != null) {
            	service.productList = response.data.scmProductList;
            	service.productList.forEach(function(item) {
            		if(item.stockAtHand == null || isNaN(item.stockAtHand)){
            			item.stockAtHand = 0;
            		}
            		if(item.inTransit == null || isNaN(item.inTransit)){
            		    item.inTransit=0;
                    }
            		if(item.saleQuantity == null || isNaN(item.saleQuantity)){
            			item.saleQuantity = 0;
            		}
					if(((item.stockAtHand+item.inTransit) - item.saleQuantity) > 0){
						item.suggestedQuantity = item.suggestedQuantity - ((item.stockAtHand+item.inTransit) - item.saleQuantity);
						if(item.suggestedQuantity < 0){
							item.suggestedQuantity = 0;
						}
					}

					item.orderingQuantity = item.suggestedQuantity;
                    if(expiryProduct[item.id]){
                        item.expiryQuantity=expiryProduct[item.id].quantity;
                    }
                    else {
                        item.expiryQuantity=0;
                    }
                    var remainingQuantity=0;
                    if(item.expiryQuantity>=item.saleQuantity && item.expiryQuantity>0) {
                        remainingQuantity = ((item.stockAtHand+item.inTransit) - item.saleQuantity - (item.expiryQuantity - item.saleQuantity));
                        if (item.expiryQuantity !== 0) {
                            if (item.orderingQuantity > remainingQuantity) {
                                item.orderingQuantity = item.orderingQuantity - remainingQuantity;
                            }
                        }
                    }
                    if(item.expiryQuantity<item.saleQuantity && item.expiryQuantity>0) {
                        remainingQuantity = ((item.stockAtHand+item.inTransit) - item.saleQuantity);
                        if (item.expiryQuantity !== 0) {
                            if (item.orderingQuantity > remainingQuantity) {
                                item.orderingQuantity = item.orderingQuantity - remainingQuantity;
                            }
                        }
                    }
					 if (item.unitOfMeasure == 'PC' || item.unitOfMeasure == 'SACHET') {
			                item.orderingQuantity = Math.ceil(item.orderingQuantity);
			            } else {
			                item.orderingQuantity = parseFloat(item.orderingQuantity).toFixed(6)
			            }
				});
                service.setMOQToProducts();
                if(!angular.isUndefined(callBack) && typeof callBack == "function"){
                    service.productList = callBack(service.productList);
                }
            } else {
                $toastService.create("Something went wrong. Please try again!");
            }
        }, function error(response) {
            $toastService.create("Something went wrong. Please try again!");
        });

    }

    function getProductsFromCategoryListNewRegularOrdering(unit, categoryList,expiryProduct,originalExpiry, finalExpiry, scmProductMap, productWiseStock,callBack) {
        console.log('Calling getProductsFromCategoryListNewRegularOrdering');
        var products = [];
        service.productList = [];
        service.getUnitProductPackagingMappings();
        categoryList.forEach(function (cat) {
            if (products.length == 0) {
                products = cat.productList;
            } else {
                products = products.concat(cat.productList);
            }
        });
        service.getProductsFromRecipe(unit,products);
        console.log('Calling stock');
        var data = {
            unitId : appUtil.getUnitData().id,
            scmProductList : service.productList
        };
        console.log('Product List ',data);
        $http({
            method: "POST",
            // url: apiJson.urls.stockManagement.getCurrentStock,
            url: apiJson.urls.stockManagement.getInTransitStock,
            // url: apiJson.urls.stockManagement.getLiveStock,
            params: {
                "isF9" : true
            },
            data: data
        }).then(function success(response) {
            console.log('Received Response for stock call', response);
            if (response.data != null) {
                service.productList = response.data.scmProductList;
                service.productList.forEach(function (item) {
                    var check = false;
                    if (item.stockAtHand == null || isNaN(item.stockAtHand)) {
                        item.stockAtHand = 0;
                    }
                    if (item.inTransit == null || isNaN(item.inTransit)) {
                        item.inTransit = 0;
                    }
                    if (productWiseStock[item.id] != undefined && productWiseStock[item.id] != null) {
                        item.checkStockAtHand = productWiseStock[item.id].stockAtHand;
                        item.checkInTransit = productWiseStock[item.id].inTransit;
                        check = true;
                    }
                    else {
                        item.checkStockAtHand = undefined;
                    }
                    if (item.saleQuantity == null || isNaN(item.saleQuantity)) {
                        item.saleQuantity = 0;
                    }
                    if (scmProductMap != undefined && scmProductMap != null) {
                        if (scmProductMap[item.id] != undefined && scmProductMap[item.id] != null) {
                            item.suggestedQuantity = scmProductMap[item.id];
                            item.orderingQuantity = scmProductMap[item.id];
                            check = true;
                            console.log("Suggested is for item id  : ", item.id, item.suggestedQuantity);
                        }
                    }
                    if (originalExpiry != undefined && originalExpiry != null) {
                        if (originalExpiry[item.id] != undefined && originalExpiry [item.id] != null) {
                            item.originalExpiryQuantity = originalExpiry[item.id];
                            console.log("original expiry is : ", item.name, item.originalExpiryQuantity)
                        } else {
                            item.originalExpiryQuantity = 0;
                        }
                    }
                    if (finalExpiry != undefined && finalExpiry != null) {
                        if (finalExpiry[item.id] != undefined && finalExpiry [item.id] != null) {
                            item.expiryQuantity = finalExpiry[item.id];
                        } else {
                            item.expiryQuantity = 0;
                        }
                    }
                    if (!check) {
                        if(((item.stockAtHand+item.inTransit) - item.saleQuantity) > 0){
                            item.suggestedQuantity = item.suggestedQuantity - ((item.stockAtHand+item.inTransit) - item.saleQuantity);
                            if(item.suggestedQuantity < 0){
                                item.suggestedQuantity = 0;
                            }
                        }

                        item.orderingQuantity = item.suggestedQuantity;

                        var remainingQuantity=0;
                        if(item.expiryQuantity>=item.saleQuantity && item.expiryQuantity>0) {
                            remainingQuantity = ((item.stockAtHand+item.inTransit) - item.saleQuantity - (item.expiryQuantity - item.saleQuantity));
                            if (item.expiryQuantity !== 0) {
                                if (item.orderingQuantity > remainingQuantity) {
                                    item.orderingQuantity = item.orderingQuantity - remainingQuantity;
                                }
                            }
                        }
                        if(item.expiryQuantity<item.saleQuantity && item.expiryQuantity>0) {
                            remainingQuantity = ((item.stockAtHand+item.inTransit) - item.saleQuantity);
                            if (item.expiryQuantity !== 0) {
                                if (item.orderingQuantity > remainingQuantity) {
                                    item.orderingQuantity = item.orderingQuantity - remainingQuantity;
                                }
                            }
                        }
                    }
                    else {
                        console.log("Used new logic for item : ",item);
                    }
                    if (item.unitOfMeasure == 'PC' || item.unitOfMeasure == 'SACHET') {
                        item.orderingQuantity = Math.ceil(item.orderingQuantity);
                    } else {
                        item.orderingQuantity = parseFloat(item.orderingQuantity).toFixed(6);
                    }
                });
                service.setMOQToProducts();
                if(!angular.isUndefined(callBack) && typeof callBack == "function"){
                    service.productList = callBack(service.productList);
                }
            }
            else {
                $toastService.create("Something went wrong. Please try again!");
            }
        }, function error(response) {
            $toastService.create("Something went wrong. Please try again!");
        });
    }

        function getUnitProductPackagingMappings() {
            $http({
                method: "GET",
                url: apiJson.urls.productManagement.defaultPackagingMappings,
                params: {
                    unitId : appUtil.getCurrentUser().unitId
                }
            }).then(function success(response) {
                if (response.data != null) {
                    service.unitProductPackagingMap = response.data;
                } else {
                    $toastService.create("Something went wrong. Please try again!");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        };

    function getProductsFromRecipe(unit,prods) {
        prods.forEach(function (product) {
            if (product.requestedQuantity != null && product.requestedQuantity >= 0) {
                //console.log(product.productName, product.requestedQuantity);
                unit.products.forEach(function (item) {
                    if (item.id == product.productId) {
                        //console.log("item mached in list");
                        item.prices.forEach(function (price) {
                            //console.log("dimensions:::", price);
                            if (price.dimension == product.dimension && price.recipe != null) {
                                addProductIngredients(product, price);
                                addProductAddons(unit,product, price);
                                addProductDineInConsumables(product, price);
                                addProductDeliveryConsumables(product, price);
                                addProductTakeawayConsumables(product, price);
                            }
                        });
                    }
                });
            }
        });

        //console.log(service.productList);
    }

    function addProductIngredients(product, price) {
        price.recipe.ingredient.components.forEach(function (component) {   // setting recipe ingredient components
            if (component.status == "ACTIVE") {
                //console.log("adding components");
                addToScmProductList(component.product, product.requestedQuantity * component.quantity, product.saleQuantity * component.quantity,component);
            }
        });
        //if (product.supportsVariantLevelOrdering) {
            product.variants.forEach(function (variant) {
                price.recipe.ingredient.variants.forEach(function (xvariant) {   // setting recipe ingredient variants
                    if (xvariant.status == "ACTIVE") {
                        xvariant.details.forEach(function (xvarDetail) {
                            if (xvarDetail.alias == variant.name) {
                                console.log("adding variants");
                                if (variant.variantLevelOrdering) {
                                    addToScmProductList(xvariant.product, variant.orderedQuantity * xvarDetail.quantity,0,xvariant);
                                } else {
                                    addToScmProductList(xvariant.product, product.requestedQuantity * xvarDetail.quantity, product.saleQuantity * xvarDetail.quantity,xvariant);
                                }
                            }
                        });
                    }
                });
            });
        //}

        price.recipe.ingredient.products.forEach(function (prod) {   // setting recipe ingredient products
            if (prod.status == "ACTIVE") {
                console.log("adding products");
                console.log(prod);
                var plength = prod.details.length;
                prod.details.forEach(function (det) {
                    if(det.defaultSetting==true){
                        addToScmProductList(det.product, ((product.requestedQuantity/2) * det.quantity),((product.saleQuantity/2) * det.quantity),prod);
                    }else{
                        addToScmProductList(det.product, ((product.requestedQuantity/((2*plength)-1)) * det.quantity), ((product.saleQuantity/((2*plength)-1)) * det.quantity),prod);
                    }
                });
            }
        });
    }

    function addProductAddons(unit,product, price) {
        var addonList = [];
        price.recipe.addons.forEach(function (addon) {   // setting recipe addons
            if (addon.status == "ACTIVE") {
                //addToScmProductList(addon.product, product.requestedQuantity * addon.quantity, addon.uom);
            	addonList.push({
                    productId: addon.product.productId,
                    productName: addon.product.name,
                    dimension: addon.dimension.code,
                    requestedQuantity: product.requestedQuantity * addon.quantity,
                    supportsVariantLevelOrdering: addon.product.variantLevelOrdering,
                    variants: []
                });
            }
        });
        //console.log(addonList);
        getProductsFromRecipe(unit,addonList);
    }

    function addProductDineInConsumables(product, price) {
        if (product.dineInQuantity != null || appUtil.getUnitData().family == "CAFE") {
        	var val = product.dineInQuantity != null ? product.dineInQuantity : product.requestedQuantity;
            price.recipe.dineInConsumables.forEach(function (dineInConsumable) {   // setting recipe dineInConsumables
                if (dineInConsumable.status == "ACTIVE") {
                    addToScmProductList(dineInConsumable.product, val * dineInConsumable.quantity, 0,dineInConsumable);
                }
            });
        }
    }

    function addProductDeliveryConsumables(product, price) {
        if (product.deliveryQuantity != null || appUtil.getUnitData().family == "DELIVERY") {
        	var val = product.deliveryQuantity != null ? product.deliveryQuantity : product.requestedQuantity;
            price.recipe.deliveryConsumables.forEach(function (deliveryConsumable) {   // setting recipe deliveryConsumables
                if (deliveryConsumable.status == "ACTIVE") {
                    addToScmProductList(deliveryConsumable.product, val * deliveryConsumable.quantity,0,deliveryConsumable);
                }
            });
        }
    }

    function addProductTakeawayConsumables(product, price) {
        if (product.takeawayQuantity != null || appUtil.getUnitData().family == "TAKE_AWAY") {
        	var val = product.takeawayQuantity != null ? product.takeawayQuantity : product.requestedQuantity;
            price.recipe.takeawayConsumables.forEach(function (takeawayConsumable) {   // setting recipe takeawayConsumables
                if (takeawayConsumable.status == "ACTIVE") {
                    addToScmProductList(takeawayConsumable.product, val * takeawayConsumable.quantity,0,takeawayConsumable);
                }
            });
        }
    }

    function addToScmProductList(product, quantity, saleQuantity, component) {
        var found = false;
        service.productList.forEach(function (item) {
            if (item.id == product.productId) {
                item.suggestedQuantity = parseFloat((parseFloat(item.suggestedQuantity) + parseFloat(quantity)).toFixed(6));
				item.orderingQuantity = parseFloat(item.suggestedQuantity);
                item.saleQuantity = parseFloat((parseFloat(item.saleQuantity) + parseFloat(saleQuantity)).toFixed(6));
                if (!item.critical) {
                    item.critical = getCriticalStatus(component);
                }
                found = true;
            }
        });
        if (!found) {
            appUtil.getActiveScmProducts().forEach(function (item) {
                if ((item.productStatus == "ACTIVE") && (item.productId == product.productId)
                    && !appUtil.isEmptyObject(item.fulfillmentType)) {

                    var subCategoryDef = appUtil.getSubCategory(item.subCategoryDefinition.id);
                    try{
                        service.productList.push({
                            id: item.productId,
                            name: item.productName,
                            subCategoryName: subCategoryDef == null ? 'NA' : subCategoryDef.subCategoryName,
                            suggestedQuantity: parseFloat(quantity),
                            saleQuantity : parseFloat(saleQuantity),
                            unitOfMeasure: item.unitOfMeasure,
                            orderingQuantity: parseFloat(quantity),
                            selectedFulfillmentType: getFulfilmentType(item),
                            supportsSpecialOrdering: item.supportsSpecialOrdering,
                            critical: getCriticalStatus(component)
                        });
                        console.log("list is : ",service.productList);
                    }catch(e){
                        console.log("Error while adding product to productList",item.productId);
                    }
                }
            });
        }
    }

    function getCriticalStatus(product) {
        if (product != undefined && product != null && product.critical != undefined && product.critical != null) {
            return product.critical;
        }
        return false;
    }

    function getFulfilmentType(product){
        var fulfillmentType = product.fulfillmentType;
        if(product.fulfillmentType=="DERIVED"){
            if(product.derivedMappings.length>0){
                product.derivedMappings.map(function (mapping) {
                    if(mapping.unit == appUtil.createRequestUnit().id){
                        fulfillmentType = mapping.type;
                    }
                });
            }else{
                fulfillmentType = product.defaultFulfillmentType;
            }
        }
        return fulfillmentType;
    }

    function setMOQToProducts(){
        var productPackaging = [];
        metaDataService.getAllPackagingMappings(function (packagingMap) {
            productPackaging = packagingMap;
            var packagingDef = appUtil.getPackagingMap();
            service.productList.forEach(function (item) {
                var duplicateOrderingQuantity = angular.copy(item.orderingQuantity);
                productPackaging[item.id].forEach(function (pack) {
                    if(pack.mappingStatus=='ACTIVE' && pack.isDefault){
                        item.packagingName = packagingDef[pack.packagingId].packagingName;
                        item.conversionRatio = packagingDef[pack.packagingId].conversionRatio;
                        item.packagingQuantity = Math.ceil(parseFloat(item.orderingQuantity/item.conversionRatio));
                        item.duplicatePackagingQuantity = Math.ceil(parseFloat(item.orderingQuantity/item.conversionRatio));
                        item.orderingQuantity = item.packagingQuantity*item.conversionRatio;
                    }
                });
                if(service.unitProductPackagingMap !=null && service.unitProductPackagingMap[item.id] != undefined){
                    service.unitProductPackagingMap[item.id].forEach(function (pack) {
                        item.packagingName = packagingDef[pack.packagingId].packagingName;
                        item.conversionRatio = packagingDef[pack.packagingId].conversionRatio;
                        item.packagingQuantity = Math.ceil(parseFloat(duplicateOrderingQuantity/item.conversionRatio));
                        item.duplicatePackagingQuantity = Math.ceil(parseFloat(duplicateOrderingQuantity/item.conversionRatio));
                        item.orderingQuantity = item.packagingQuantity*item.conversionRatio;
                    });
                }
            });
        },true);
    }

    return service;
}]);
