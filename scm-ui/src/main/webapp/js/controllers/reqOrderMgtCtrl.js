/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('reqOrderMgtCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$state','$toastService',
        function ($rootScope, $scope, apiJson, $http, appUtil,$state, $toastService) {

            $scope.init = function () {
                var reqOrderListObj = appUtil.getReqOrderListObj();
                $scope.scmOrderStatusList = [];
                $scope.scmOrderStatusList.push("");
                $scope.scmOrderStatusList = $scope.scmOrderStatusList.concat(appUtil.getMetadata().scmOrderStatus);
                $scope.status = reqOrderListObj.status==null?$scope.scmOrderStatusList[0]:reqOrderListObj.status;
                $scope.vendorId = reqOrderListObj.vendorId==null?null:reqOrderListObj.vendorId;
                $scope.productId = reqOrderListObj.productId==null?null:reqOrderListObj.productId;
                $scope.requestOrderList = reqOrderListObj.requestOrderList;
                $scope.requestOrderId = reqOrderListObj.requestOrderId;
                $scope.startDate = reqOrderListObj.startDate==null?appUtil.formatDate(new Date(), "yyyy-MM-dd"):reqOrderListObj.startDate;
                $scope.endDate = reqOrderListObj.endDate==null?appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd"):reqOrderListObj.endDate;
                $scope.searchTag = reqOrderListObj.searchTag == null?null:reqOrderListObj.searchTag;
                $scope.unitList = [];
                $scope.unitList.push({
                    id: null,
                    name: ""
                });
                $scope.unitList = $scope.unitList.concat(appUtil.getUnitList());
                $scope.fulfillingUnit = reqOrderListObj.fulfillingUnit==null?$scope.unitList[0]:reqOrderListObj.fulfillingUnit;
                $scope.requestOrderList = reqOrderListObj.requestOrderList;
                $scope.findRequestOrders();
            };

            $scope.getProducts = function(){
              $scope.productList = appUtil.getActiveScmProducts();
            };

            $scope.getVendors = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.vendorManagement.vendors
                }).then(function success(response) {
                    $scope.vendorList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };


            $scope.findRequestOrders = function(){
                if($scope.startDate==null || $scope.startDate.trim() == ''|| $scope.endDate==null || $scope.endDate.trim() == ''){
                    $toastService.create("Please fill start date and end date properly!");
                    return false;
                }else if($scope.startDate != null && $scope.endDate != null && $scope.startDate > $scope.endDate) {
	           		 $toastService.create("Please fill end date greater than or equal to start date properly!");
	                 return false;
                }else{
                    var url = apiJson.urls.requestOrderManagement.requestOrderFind+"?requestingUnitId="+
                        appUtil.getCurrentUser().unitId+"&startDate="+$scope.startDate+"&endDate="+$scope.endDate;
                    if($scope.requestOrderId!=null){
                        url+="&requestOrderId="+$scope.requestOrderId;
                    }
                    if($scope.fulfillingUnit.id!=null){
                        url+="&fulfillingUnitId="+$scope.fulfillingUnit.id;
                    }
                    if($scope.status!=null && $scope.status!=""){
                        url+="&status="+$scope.status;
                    }
                    if($scope.vendorId!=null && $scope.vendorId!=""){
                        url+="&vendorId="+$scope.vendorId;
                    }
                    if($scope.productId!=null && $scope.productId!=""){
                        url+="&productId="+$scope.productId;
                    }
                    if($scope.searchTag!=null && $scope.searchTag!=""){
                        url+="&searchTag="+$scope.searchTag;
                    }
                    $http({
                        method: "GET",
                        url: url
                    }).then(function success(response) {
                        $scope.requestOrderList = response.data;
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

            $scope.isLastCreated = function(item){
            	var myStartDate = new Date(new Date() - 120000);
            	return item.generationTime > myStartDate;
            };
            
            $scope.openReqOrderAction = function(roId){
                $rootScope.selectedReqOrderId = roId;
                appUtil.setReqOrderListObj({
                    startDate: $scope.startDate,
                    endDate: $scope.endDate,
                    requestOrderId: $scope.requestOrderId,
                    fulfillingUnit: $scope.fulfillingUnit,
                    vendorId:$scope.vendorId,
                    productId:$scope.productId,
                    status: $scope.status,
                    searchTag: $scope.searchTag,
                    requestOrderList: $scope.requestOrderList
                });
                $state.go("menu.reqOrderAction");
            };

            $scope.cloneRO = function (ro) {
                $state.go("menu.adhocOrderCreate", {clonedItems: ro.requestOrderItems, "fulfilmentUnit" : ro.fulfillmentUnit.name});
            };
        }
    ]);
