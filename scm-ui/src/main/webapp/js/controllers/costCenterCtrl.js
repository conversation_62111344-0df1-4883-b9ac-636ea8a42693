angular.module('scmApp').controller(
		'createCostCenterCtrl',
		[
				'$rootScope',
				'$scope',
				'apiJson',
				'$http',
				'appUtil',
				'$toastService',
				'metaDataService',
				'$timeout',
				function($rootScope, $scope, apiJson, $http, appUtil,
						$toastService, metaDataService, $timeout) {
					
					$scope.init = function () {
						console.log("running cost center");
						$scope.getCostCenters();
					}
					
					$scope.getCostCenters = function () {
	                        $http({
	                            url: apiJson.urls.serviceOrderManagement.costCentersAll,
	                            method: "GET",
	                        }).then(function (response) {
	                            $scope.allCostCenters = response.data;
	                        }, function (response) {
	                            console.log(response);
	                        });
	                };
					
	                $scope.openAddNewCostCenterModal = function(){
	                	 $scope.getEmployees();
	                	 $scope.costCenter = {};
	                }
	                
	                $scope.getEmployees = function () {
	                    $http({
	                        method: "GET",
	                        url: apiJson.urls.users.activeUsers
	                    }).then(function success(response) {
	                        $scope.employees = response.data;
	                    }, function error(response) {
	                        console.log("error:" + response);
	                    });
	                };
	                
	                $scope.createCostCenter = function() {
	                	for(var x = 0; x < $scope.allCostCenters.length ;x++){
	                		if($scope.allCostCenters[x].name == $scope.costCenter.name){
	                			$toastService.create("Duplicate Cost Center Name");
	                			return;
	                		}
	                	}
	                	 $http({
	                         url: apiJson.urls.serviceOrderManagement.costCentersCreate,
	                         method: 'POST',
	                         data: $scope.costCenter
	                     }).then(function (response) {
	                         $toastService.create("Cost Center Added!");
	                         $scope.getCostCenters();
	                         $("#addNewCostCenter").closeModal();
	                         $timeout(function () {
	                             $('#ownerSelector').val('').trigger('change');
	                         });
	                     }, function error(response) {
	                         console.log("got error", response);
	                     });
	                };
	                
	                $scope.clearCostCenter = function() {
	                	 $scope.costCenter = {};
	                };
	                
				} ]);