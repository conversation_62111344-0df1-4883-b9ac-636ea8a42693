/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


var scmApp = angular.module("vendorApp", ['ui.router', 'ui.materialize', 'ui.select2', 'dndLists', 'ngCookies', 'AngularPrint', 'fsm']);

scmApp.config(['$stateProvider', '$urlRouterProvider', function ($stateProvider, $urlRouterProvider) {

    $stateProvider.state('registerVendor', {
        url: "/vendorCreate",
        templateUrl: 'views/vendors/vendorRegistration.html',
        controller: 'vendorCtrl'
    }).state('vendorCompany', {
        url: "/company",
        templateUrl: 'views/vendors/companyRegistration.html',
        params: {basicDetail: null},
        controller: 'vendorCompanyCtrl'
    }).state('vendorAccount', {
        url: "/account",
        templateUrl: 'views/vendors/accountRegistration.html',
        params: {basicDetail: null},
        controller: 'vendorAccountCtrl'
    }).state('vendorLocation', {
        url: "/locations",
        templateUrl: 'views/vendors/vendorLocations.html',
        params: {basicDetail: null},
        controller: 'vendorLocationCtrl'
    }).state('vendorError', {
        url: "/vendorError",
        templateUrl: 'views/vendors/vendorCompletion.html',
        controller: 'vendorCompletionCtrl'
    }).state('vendorSuccess', {
        url: "/vendorSuccess",
        templateUrl: 'views/vendors/vendorCompletion.html',
        controller: 'vendorCompletionCtrl'
    });

    $urlRouterProvider.otherwise("/vendorCreate");
}]).service('$toastService', ['$rootScope', '$window', function ($rootScope, $window) {
    var service = this;
    var defaultDuration = 4000;

    service.create = function (message, callback) {
        if ($window.Materialize) {
            Materialize.toast(message, defaultDuration);
            if (callback != undefined) {
                callback();
            }
        }
    };
}]).service('$alertService', ['$rootScope', '$window', function ($rootScope, $window) {
    var service = this;
    var defaultDuration = 4000;

    service.alert = function (title, message, callback) {
        if ($window.materialAlert) {
            materialAlert(title, message, callback);
        }
    };

    service.confirm = function (title, message, callback) {
        if ($window.materialConfirm) {
            materialConfirm(title, message, callback);
        }
    };

    service.closeMaterialAlert = function () {
        if ($window.closeMaterialAlert) {
            closeMaterialAlert(e, result);
        }
    };

}]).service('$fileUploadService', ['$rootScope', '$window', function ($rootScope, $window) {
    var service = this;
    var defaultDuration = 4000;

    service.openFileModal = function (title, message, callback) {
        if ($window.showFileModal) {
            showFileModal(title, message, callback);
        }
    };

    service.closeMaterialAlert = function () {
        if ($window.closeFileModal) {
            closeFileModal(e);
        }
    };

}]).service('fileService', function () {
    var service = this;
    service.file = null;
    service.push = function (file) {
        service.file = file;
    };
    service.getFile = function () {
        return service.file;
    };
}).factory('httpAuthInterceptor', ['$q', '$rootScope', 'authService', '$location', function ($q, $rootScope, authService, $location) {
    return {
        request: function (config) {
            $rootScope.showSpinner = true;
            config.headers["auth-internal"] = authService.getAuthorization();
            return config;
        },
        requestError: function (rejection) {
            $rootScope.showSpinner = false;
            return $q.reject(rejection);
        },
        response: function (response) {
            $rootScope.showSpinner = false;
            return response || $q.when(response);
        },

        responseError: function (response) {
            $rootScope.showSpinner = false;
            if (response.status === 401) {
                $location.path('/');
            }
            return $q.reject(response);
        }
    };
}]).config(['$httpProvider', function ($httpProvider) {
    $httpProvider.interceptors.push('httpAuthInterceptor');
}]).run(['$rootScope', 'authService', '$state', '$stateParams', 'authService', '$location',
    function ($rootScope, authService, $state, $stateParams) {
        console.log("inside run function");
        $rootScope.v = 1;
        $rootScope.$state = $state;
        $rootScope.$stateParams = $stateParams;
        $rootScope.showSpinner = false;
        $rootScope.showOverlay = false;
    }
]).directive('validateForm', [function () {
    return {
        link: function (scope, element, attrs) {
            element.on('click', function () {
                scope.$apply(function () {
                    validateForm(attrs.validateForm);
                });
            });
        }
    };

}]).directive('validate', [function () {
    return {
        link: function (scope, element, attrs) {
            element.on('change', function () {
                scope.$apply(function () {
                    if(attrs.maxlength){
                        validateLength(attrs.maxlength,element);
                    }
                    validateInput(attrs.validate,element);
                });
            });
        }
    };

}]).directive('stringToNumber', [function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attrs, ngModel) {
            ngModel.$parsers.push(function (value) {
                return '' + value;
            });
            ngModel.$formatters.push(function (value) {
                return parseFloat(value, 10);
            });
        }
    };
}]).directive('fileModel', ['$parse', 'fileService', function ($parse, fileService) {
    return {
        restrict: 'A',
        link: function (scope, element, attrs) {
            //var model = $parse(attrs.fileModel);
            //var modelSetter = model.assign;
            element.bind('change', function () {
                scope.$apply(function () {
                    fileService.push(element[0].files[0]);
                    //modelSetter(scope, element[0].files[0]);
                });
            });
        }
    };

}]).directive('createAddress', [function () {
        return {
            restrict: 'A',
            require: 'ngModel',
            replace: true,
            scope: {
                ngModel: '=ngModel'
            },
            link: function compile(scope, element) {
                scope.$watch('ngModel', function (address) {
                    var addressForStr = angular.copy(address);
                    var values = Object.keys(address).filter(function(key){
                        return key != "addressId" && key!="addressType" && key!="locationId" && key!="stateCode";
                    }).map(function(key){
                        return address[key];
                    });

                    var addressStr = values.filter(function (val) {
                        return val;
                    }).join(", ");
                    element.html(addressStr.toUpperCase());
                });
            }
        };
    }
]).filter('propsFilter', [function () {
    return function (items, props) {
        var out = [];
        if (angular.isArray(items)) {
            items.forEach(function (item) {
                var itemMatches = false;
                var keys = Object.keys(props);
                for (var i = 0; i < keys.length; i++) {
                    var prop = keys[i];
                    var text = props[prop].toLowerCase();
                    if (item[prop].toString().toLowerCase().indexOf(text) !== -1) {
                        itemMatches = true;
                        break;
                    }
                }
                if (itemMatches) {
                    out.push(item);
                }
            });
        } else {
            // Let the output be the input untouched
            out = items;
        }
        return out;
    };
}]);

