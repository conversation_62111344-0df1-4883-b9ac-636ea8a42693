/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */
'use strict';

(function () {
    angular.module('vendorApp').factory('appUtil', AppUtil);
    AppUtil.$inject = ['$rootScope','$http','$toastService','apiJson'];
    function AppUtil($rootScope, $http, $toastService, apiJson) {
        var service = {};
        service.createRequestUnit = createRequestUnit;
        service.createGeneratedBy = createGeneratedBy;
        service.formatDate = formatDate;
        service.getCurrentBusinessDate = getCurrentBusinessDate;

        function isImage(fileExt){
            return fileExt=="jpg" || fileExt == "jpeg" || fileExt=="png";
        }

        function getFileExtension(fileName) {
            var re = /(?:\.([^.]+))?$/;
            return re.exec(fileName)[1];
        }

        service.getCities = function(callback){
            if(service.isEmptyObject($rootScope.cities)){
                $http.get(apiJson.urls.vendorManagement.getLocations).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!service.isEmptyObject(response)) {
                        $rootScope.cities = response;
                        callback(response);
                    }else{
                        $toastService.create("Download of cities failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Download of cities failed");
                });
            }else{
                return $rootScope.cities;
            }

        };

        service.uploadFile = function(docType,type,vendorId,file,callback){
            if (file == null) {
                $toastService.create('File cannot be empty');
                return;
            }
            var fileName = file.name;
            var fileExt = getFileExtension(fileName);
            if (fileExt.toLowerCase() == 'pdf' || isImage(fileExt.toLowerCase())) {
                var mimeType = fileExt.toUpperCase();
                var fd = new FormData();
                fd.append('type', type);
                fd.append('docType',docType);
                fd.append('mimeType',mimeType);
                fd.append('vendorId',vendorId);
                fd.append('file', file);
                $http.post(apiJson.urls.vendorManagement.uploadDocument, fd, {
                    transformRequest: angular.identity,
                    headers: {
                        'Content-Type': undefined
                    }
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!service.isEmptyObject(response)) {
                        callback(response);
                    }else{
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            } else {
                $toastService.create('Upload Failed , File Format not Supported');
            }
        };

        service.getDate = function(days){
            var time = new Date();
            time.setDate(time.getDate() + days);
            return time.toISOString();
        };
        service.calculatedDate = function(days, date){
        	var time = null;
        	if(angular.isUndefined(date) || date == null){
        		time = new Date();
        	}else{
        		time = new Date(date);
        	}
           time.setDate(time.getDate() + days);
           return time;
        };

        service.getFormattedDate = function(milliseconds){
            var time = new Date(milliseconds);
            var year = time.getFullYear();
            var month = time.getMonth()+1;
            var date = time.getDate();
            return date+"-"+ month + "-" + year;
        };

        service.createVendor = function(vendor){
            return {
                id:vendor.vendorId,
                code:vendor.type,
                name:vendor.entityName
            };
        };

        service.getTimeInPast = function(days){
            var time = new Date();
            time.setDate(time.getDate() - days);
            return time.toISOString();
        };

        service.getTimeInFuture = function(days){
            var time = new Date();
            time.setDate(time.getDate() + days);
            return time.toISOString();
        };


        service.isCafe = function(){
            return (!service.isEmptyObject(service.getUnitData()) && service.getUnitData().family == "CAFE");
        };

        function getCurrentBusinessDate() {
            var time = new Date();
            if(time.getHours() < 5){
                time.setDate(time.getDate() - 1);
            }
            console.log(time.toDateString());
            return formatDate(time,"yyyy-MM-dd");
        }


        service.isEmptyObject = function (obj) {
            if (obj != undefined && obj != null) {
                if (typeof obj == 'string' || typeof obj == 'number')
                    return obj.toString().length == 0;
                else
                    return Object.keys(obj).length == 0;
            }
            return true;
        };

        service.checkEmpty = function (str) {
            var type = typeof str;
            if (type == 'string') {
                return str.trim().length == 0;
            } else if (type == "number") {
                return str == -1;
            } else if (type == null || type == undefined) {
                return true;
            } else {
                return service.isEmptyObject(str);
            }
        };

        function createRequestUnit() {
            var currentUser = this.getCurrentUser();
            return {
                id: currentUser.unitId,
                code: null,
                name: "Good Earth City Center"
            }
        }

        function createGeneratedBy() {
            var currentUser = this.getCurrentUser();
            return {
                id: currentUser.user.id,
                code: null,
                name: currentUser.user.name
            }
        }

        function formatDate(date, format){
            var time = new Date(date);
            var yyyy = time.getFullYear();
            var M = time.getMonth()+1;
            var d = time.getDate();
            var MM = M;
            var dd = d;
            var hh = time.getHours();
            var mm = time.getMinutes();
            var ss = time.getSeconds();
            if(M<10){
                MM = "0"+M;
            }
            if(d<10){
                dd = "0"+d;
            }
            if(format=="yyyy-MM-dd"){
                return yyyy+"-"+MM+"-"+dd;
            }
            if(format=="dd/MM/yyyy"){
                return dd+"/"+MM+"/"+yyyy;
            }
            if(format=="dd-MM-yyyy-hh-mm-ss"){
                return dd+"-"+MM+"-"+yyyy+"-"+hh+"-"+mm+"-"+ss;
            }
            if(format=="dd-MM-yyyy"){
                return dd+"-"+MM+"-"+yyyy;
            }
        }
        return service;
    }

    angular.module('vendorApp').factory('apiJson', APIJson);
    function APIJson() {
        var service = this;
        var basePath = window.location.protocol+"//"+window.location.host;
        basePath = basePath + "/scm-service/rest/v1/vendor-registration-management/";

        service.urls = {
            vendorManagement: {
                getAuth: basePath + "get-auth",
                getVendor: basePath + "get-vendor",
                validateRequest: basePath + "validate",
                saveBasicDetails: basePath + "save-basic-detail",
                saveCompanyDetails: basePath + "save-company-detail",
                saveAccountDetails: basePath + "save-account-detail",
                saveLocations: basePath + "save-locations",
                uploadDocument: basePath + "upload-document",
                getLocations: basePath + "delivery-locations",
                checkDuplicatePanNumber: basePath + "vendor-duplicate-pan-check",
                checkDuplicateVendorName : basePath + "vendor-duplicate-name-check"
            }
        };
        return service;
    }
})();

