package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.inventory.service.InventoryService;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.tax.model.AdditionalTax;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.GatepassException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.AbstractStockManagementService;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.GatepassManagementService;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.GatepassManagementDao;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.GatepassData;
import com.stpl.tech.scm.data.model.GatepassItemData;
import com.stpl.tech.scm.data.model.GatepassItemDrilldownDetail;
import com.stpl.tech.scm.data.model.GatepassStatusDetailData;
import com.stpl.tech.scm.data.model.GatepassTaxDetail;
import com.stpl.tech.scm.data.model.GatepassVendorMappingData;
import com.stpl.tech.scm.domain.model.Gatepass;
import com.stpl.tech.scm.domain.model.GatepassItem;
import com.stpl.tech.scm.domain.model.GatepassItemAssetMapping;
import com.stpl.tech.scm.domain.model.GatepassOperationType;
import com.stpl.tech.scm.domain.model.GatepassReturnStatus;
import com.stpl.tech.scm.domain.model.GatepassStatus;
import com.stpl.tech.scm.domain.model.GatepassTransType;
import com.stpl.tech.scm.domain.model.GatepassVendorMapping;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.ProductBasicDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.SearchGatepass;
import com.stpl.tech.scm.domain.model.SkuBasicDetail;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.data.model.GatepassItemAssetMappingData;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GatepassManagementServiceImpl extends AbstractStockManagementService implements GatepassManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(GatepassManagementServiceImpl.class);

	@Autowired
	private GatepassManagementDao gatepassManagementDao;
	@Autowired
	private MasterDataCache masterDataCache;
	@Autowired
	private SCMCache scmCache;
	@Autowired
	private PriceManagementDao priceDao;
	@Autowired
	private TaxDataCache taxCache;
	@Autowired
	private StockManagementDao stockManagementDao;

	@Autowired
	private InventoryService inventoryService;
	@Autowired
	private EnvProperties props;

	@Autowired
	private SCMAssetManagementService scmAssetManagementService;

	@Autowired
	private StockManagementService stockManagementService;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<GatepassVendorMapping> getVendorMappingList(String opsType, Integer unitId, String status) {
		List<GatepassVendorMappingData> vendordata = gatepassManagementDao.getVendorMappingList(opsType, unitId, null,
				status);
		List<GatepassVendorMapping> vendorDetail = new ArrayList<>();
		vendordata.forEach(data -> {
			VendorDetail vendor = scmCache.getVendorDetail(data.getVendorId());
			if(Objects.nonNull(vendor) && (Objects.isNull(status) ||  vendor.getStatus().name().equals(status))) {
				vendorDetail.add(SCMDataConverter.convert(data, scmCache, masterDataCache));
			}
		});
		return vendorDetail;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Integer createGatepass(Gatepass gatepass) throws InventoryUpdateException, GatepassException, SumoException, ParseException, DataNotFoundException {
		GatepassData gatepassData = new GatepassData();
		List<GatepassVendorMappingData> getVendorMappingList = gatepassManagementDao.getVendorMappingList(gatepass.getOperationType().name(),gatepass.getSendingUnit().getId(),gatepass.getVendor().getId(),"ACTIVE");
		if(getVendorMappingList.isEmpty()){
			throw new SumoException("INACTIVE_VENDOR","vendor is in-active : "+gatepass.getVendor().getId());
		}
		gatepassData.setVendorId(gatepass.getVendor().getId());
		gatepassData.setOperationType(gatepass.getOperationType().name());
		if (gatepass.getReturnable() != null && gatepass.getReturnable()) {
			gatepassData.setReturnable(SCMUtil.setStatus(gatepass.getReturnable()));
		}else{
            gatepassData.setReturnable(SCMServiceConstants.SCM_CONSTANT_NO);
        }
		if (gatepass.getExpectedReturn() != null) {
			gatepassData.setExpectedReturn(gatepass.getExpectedReturn());
		}

		GatepassStatus status = GatepassStatus.CLOSED;
		if(gatepass.getReturnable() != null && gatepass.getReturnable()){
            status = GatepassStatus.PENDING_RETURN;
        }
		gatepassData.setStatus(status.name());

		gatepassData.setTotalCost(BigDecimal.ZERO);
		gatepassData.setTotalTax(BigDecimal.ZERO);

		if (gatepass.getAdditionalCharges() != null) {
			gatepassData.setAdditionalCharges(gatepass.getAdditionalCharges());
		}

		gatepassData.setComment(gatepass.getComment());
		gatepassData.setReason(gatepass.getReason());
		gatepassData.setCreatedBy(gatepass.getCreatedBy().getId());
		gatepassData.setCreatedAt(AppUtils.getCurrentTimestamp());
		gatepassData.setSendingUnit(gatepass.getSendingUnit().getId());
		gatepassData.setSendingCompany(masterDataCache.getUnit(gatepassData.getSendingUnit()).getCompany().getId());
		gatepassData.setNeedsApproval(AppUtils.NO);
		gatepassData.setIssueDate(gatepass.getIssueDate());
		gatepassData.setDispatchLocationId(gatepass.getDispatchLocation().getId());
		gatepassData.setAssetGatePass(SCMUtil.setStatus(gatepass.getAssetGatePass()));
		setGatepassItemData(gatepass, gatepassData);
		GatepassData data = gatepassManagementDao.add(gatepassData, true);

		if (data != null) {
			Gatepass finalGatepass = SCMDataConverter.convert(data, false, scmCache, masterDataCache,true);
			// verifying inventory during gatepass
			stockManagementService.verifyInventoryForKeys(finalGatepass, false);
			finalGatepass = priceDao.reduceConsumable(finalGatepass, false);
			setPriceTaxDetails(finalGatepass, gatepassData);
			setItemDrillDowns(finalGatepass, gatepassData);
			setGatepassStatusDetail(GatepassStatus.INITIATED.name(), status.name(),
                    gatepass.getCreatedBy().getId(), data.getId(), true);
			/*
				Check for if gatepass contains assets, if present create mapping for them
			*/
			if(gatepass.getAssetGatePass()) {
				// change status of gatepass and insert appropriate details
				int index = 0;
				for(GatepassItemData gatepassItemData: data.getItemDatas()) {
					List<GatepassItemAssetMapping> list = gatepass.getItemDatas().get(index).getGatepassItemAssetMappings();
					populateGatePassItemAssetMapping( list, gatepassItemData, data);
					boolean response = scmAssetManagementService.transferAssetAgainstGatepass(list , data);
					if(!response){
						return null;
					}
					index++;
				}
			}
			return data.getId();
		}
		return null;
	}

	private void setPriceForAssetGatePass(GatepassData gatepassData,Gatepass gatepass ){
        gatepassData.setTotalCost(gatepass.getTotalCost());
        gatepassData.setTotalTax(gatepass.getTotalTax());
	}

	private void populateGatePassItemAssetMapping(List<GatepassItemAssetMapping> gatepassItemAssetMappings, GatepassItemData gatepassItemData, GatepassData gatepassData) {
		for(GatepassItemAssetMapping itemAssetMapping : gatepassItemAssetMappings){
			itemAssetMapping.setGatePassId(gatepassData.getId());
			itemAssetMapping.setGatePassItemId(gatepassItemData.getId());
			itemAssetMapping.setGatePassType(GatepassOperationType.valueOf(gatepassData.getOperationType()));
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Gatepass> getGatepass(SearchGatepass gatepass) {
		List<GatepassData> datas= new ArrayList<>();
		if(gatepass.getGatePassId()!=null){
			datas = Arrays.asList(gatepassManagementDao.find(GatepassData.class,gatepass.getGatePassId()));
		} else{
			datas = gatepassManagementDao.getGatepass(gatepass);
		}
		List<Gatepass> resultList = new ArrayList<>();
		datas.forEach(data -> {
			resultList.add(SCMDataConverter.convert(data, true, scmCache, masterDataCache,false));
		});
		return resultList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Gatepass> getGatepassDetails(SearchGatepass gatepass) {
		List<GatepassData> datas= new ArrayList<>();
		datas = Arrays.asList(gatepassManagementDao.find(GatepassData.class,gatepass.getGatePassId()));
		List<Gatepass> resultList = new ArrayList<>();
		datas.forEach(data -> {
			resultList.add(SCMDataConverter.convert(data, true, scmCache, masterDataCache,true));
		});
		resultList.forEach(data -> {
			if(data.getAssetGatePass()) {
                addAssociatedAssetsAgainstGatePass(data);
			}
		});
		return resultList;
	}

	private void addAssociatedAssetsAgainstGatePass(Gatepass gatepass) {
	    /*
            Get Assets associated to Gate pass item
        */
        for(GatepassItem gatepassItem: gatepass.getItemDatas()) {
            List<GatepassItemAssetMapping> gatepassItemAssetMappings = scmAssetManagementService.getGatepassItemAssetMappingsForGatepassItemId(gatepassItem.getId());
            gatepassItem.setGatepassItemAssetMappings(gatepassItemAssetMappings);
        }
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean updateGatepass(Gatepass gatepass) throws InventoryUpdateException, GatepassException, SumoException, ParseException, DataNotFoundException {
		GatepassData gatepassData = gatepassManagementDao.find(GatepassData.class, gatepass.getId());
		if(gatepass.getAssetGatePass()) {
		    boolean flag = scmAssetManagementService.transferAssetOnGatepassReturn(gatepass);
		    if(!flag){
		        return flag;
            }
        }
		List<GatepassItemData> itemDatas = new ArrayList<>();
		Map<Integer, GatepassItem> itemMap = new HashMap<>();
		BigDecimal totalQuantity = BigDecimal.ZERO;
		GatepassTransType transType = null;
		for (GatepassItem item : gatepass.getItemDatas()) {
			GatepassItemData itemData = new GatepassItemData();
			itemData.setSkuId(item.getSku().getId());
			transType = item.getTransType();
			itemData.setTransType(item.getTransType().name());
			itemData.setUom(item.getUom());
			itemData.setQuantity(item.getQuantity());
			totalQuantity = AppUtils.add(totalQuantity, item.getQuantity());
			itemData.setPrice(item.getPrice());
			BigDecimal tax = BigDecimal.ZERO;
			itemData.setTax(tax);
			itemData.setCost(AppUtils.multiply(item.getQuantity(), item.getPrice()));
			itemData.setAmount(AppUtils.add(itemData.getCost(), tax));
			itemData.setGatepassData(gatepassData);
			itemData.setCreatedBy(gatepass.getCreatedBy().getId());
			itemData.setCreatedAt(gatepass.getCreatedAt());
			GatepassItemData gatepassItemData = gatepassManagementDao.find(GatepassItemData.class, item.getId());
			LOG.info(" Skuid : " + itemData.getSkuId() + " Corresponding transfer item data id : " + item.getId());

			updateItemDrillDown(item, gatepassItemData);
			itemData = gatepassManagementDao.add(itemData, false);
			itemMap.put(itemData.getId(), item);
			itemDatas.add(itemData);
		}

		gatepassManagementDao.flush();
		/*
		    IF Gate pass is for assets then dont book wastage
		 */
		if(!gatepass.isAssetOrder()) {
			gatepass = priceDao.addReceiving(gatepass, false);
			if (transType == GatepassTransType.LOST) {
				LOG.info("Booking watsage " + transType);
				addLostItemsToWastage(itemDatas, itemMap);
			}
		}

		BigDecimal transferred = BigDecimal.ZERO;
		BigDecimal returned = BigDecimal.ZERO;
		BigDecimal lost = BigDecimal.ZERO;
		for (GatepassItemData itemData : gatepassData.getItemDatas()) {
			switch (GatepassTransType.valueOf(itemData.getTransType())) {
			case TRANSFER:
				transferred = AppUtils.add(transferred, itemData.getQuantity());
				break;
			case RETURN:
				returned = AppUtils.add(returned, itemData.getQuantity());
				break;
			case LOST:
				lost = AppUtils.add(lost, itemData.getQuantity());
				break;
			}
		}

		LOG.info("transferred : " + transferred + "  - returned : " + returned + " - lost : " + lost);
		if (returned.compareTo(BigDecimal.ZERO) > 0) {
			if (transferred.compareTo(returned) == 0) {
				gatepassData.setReturnStatus(GatepassReturnStatus.COMPLETE.name());
			} else {
				gatepassData.setReturnStatus(GatepassReturnStatus.PARTIAL.name());
			}
		}
		if (lost.compareTo(BigDecimal.ZERO) > 0) {
			gatepassData.setHasLoss(AppUtils.setStatus(true));
		}

		if (transferred.compareTo(AppUtils.add(returned, lost)) == 0) {
			setGatepassStatusDetail(GatepassStatus.PENDING_RETURN.name(), GatepassStatus.CLOSED.name(),
					gatepass.getCreatedBy().getId(), gatepassData.getId(), true);
			gatepassData.setStatus(GatepassStatus.CLOSED.name());
		}

		Map<Integer,Integer> assetSkuMap = new HashMap<>();
		Map<Integer,Integer> skuItemMap = new HashMap<>();
		List<Integer> returnedGatepassAssetId = new ArrayList<>();

		if (gatepass.getAssetGatePass()) {
			for (GatepassItem gatepassItem : gatepass.getItemDatas()) {
				if (gatepassItem.getTransType().equals(GatepassTransType.RETURN)) {
					if(Objects.nonNull(gatepassItem.getGatepassItemAssetMappings())){
						for (GatepassItemAssetMapping gatepassItemAssetMapping : gatepassItem.getGatepassItemAssetMappings()) {
							returnedGatepassAssetId.add(gatepassItemAssetMapping.getGatePassItemAssetId());
							assetSkuMap.put(gatepassItemAssetMapping.getAssetId(),gatepassItem.getSku().getId());
						}
					}
				}
			}
		}

		if(gatepass.isAssetOrder()) {
			for(GatepassItemData item : itemDatas){
				skuItemMap.put(item.getSkuId(),item.getId());
			}
		}

		for (Integer assetItemId : returnedGatepassAssetId) {
			GatepassItemAssetMappingData gatepassItemAssetMappingData = gatepassManagementDao.find(GatepassItemAssetMappingData.class, assetItemId);
			if(Objects.nonNull(gatepassItemAssetMappingData)){
				gatepassItemAssetMappingData.setIsReturned(SCMServiceConstants.SCM_CONSTANT_YES);
				if(Objects.nonNull(skuItemMap.get(assetSkuMap.get(gatepassItemAssetMappingData.getAssetId())))){
					gatepassItemAssetMappingData.setReturnGatePassItemId(skuItemMap.get(assetSkuMap.get(gatepassItemAssetMappingData.getAssetId())));
				}
				gatepassManagementDao.update(gatepassItemAssetMappingData, true);
			}
		}

		gatepassData = gatepassManagementDao.update(gatepassData, true);
		return gatepassData != null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean cancelGatepass(Gatepass gatepass) throws InventoryUpdateException, SumoException {
		GatepassData gatepassData = gatepassManagementDao.find(GatepassData.class, gatepass.getId());
		for (GatepassItem item : gatepass.getItemDatas()) {
			updateItemDrillDown(item);
		}
		gatepass = priceDao.addReceiving(gatepass, true);
		gatepassData.setStatus(GatepassStatus.CANCELLED.name());
		gatepassData.setCancelledBy(gatepass.getCancelledBy().getId());
		gatepassData.setCancelledAt(AppUtils.getCurrentTimestamp());
		gatepassData = gatepassManagementDao.update(gatepassData, true);
		if (gatepassData != null) {
			setGatepassStatusDetail(GatepassStatus.PENDING_RETURN.name(), GatepassStatus.CANCELLED.name(),
					gatepass.getCancelledBy().getId(), gatepassData.getId(), true);
			return true;
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean addVendorMapping(GatepassVendorMapping vendorMapping) throws GatepassException, SumoException {

		List<GatepassVendorMappingData> datas = gatepassManagementDao.getVendorMappingList(
				vendorMapping.getOperationType(), vendorMapping.getUnit().getId(), vendorMapping.getVendorId(), null);
		if (datas.size() > 0) {
			throw new GatepassException("Mapping already Exist for this unit operation and vendor");
		}
		GatepassVendorMappingData detailData = new GatepassVendorMappingData();
		detailData.setOperationType(vendorMapping.getOperationType());
		detailData.setVendorId(vendorMapping.getVendorId());
		detailData.setUnitId(vendorMapping.getUnit().getId());
		detailData.setCreatedBy(vendorMapping.getCreatedBy().getId());
		detailData.setCreatedAt(AppUtils.getCurrentTimestamp());
		detailData.setStatus(ProductStatus.ACTIVE.name());
		detailData = gatepassManagementDao.add(detailData, true);

		if (detailData == null) {
			return true;
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean updateVendorStatus(int mappingId, String status) {
		GatepassVendorMappingData mappingData = gatepassManagementDao.find(GatepassVendorMappingData.class, mappingId);
		mappingData.setStatus(status);
		mappingData = gatepassManagementDao.update(mappingData, true);
		if (mappingData != null) {
			return true;
		}
		return null;
	}

	private void updateItemDrillDown(GatepassItem item) {
		GatepassItemData gatepassItemData = gatepassManagementDao.find(GatepassItemData.class, item.getId());
		List<GatepassItemDrilldownDetail> drilldownDetails = gatepassItemData.getDrillDowns();
		List<InventoryItemDrilldown> drilldowns = new ArrayList<>();
		for (GatepassItemDrilldownDetail detail : drilldownDetails) {
			drilldowns.add(SCMDataConverter.convert(detail, detail.getQuantity()));
		}
		item.getDrillDowns().addAll(drilldowns);
	}

	private void updateItemDrillDown(GatepassItem item, GatepassItemData gatepassItemData) {
		List<GatepassItemDrilldownDetail> drilldownDetails = gatepassItemData.getDrillDowns();
		sortCollections(drilldownDetails);
		List<InventoryItemDrilldown> drilldowns = new ArrayList<>();
		BigDecimal quantity = item.getQuantity();
		for (GatepassItemDrilldownDetail detail : drilldownDetails) {

			if (quantity.compareTo(BigDecimal.ZERO) > 0 && detail.getRemainingQuantity() != null
					&& detail.getRemainingQuantity().compareTo(BigDecimal.ZERO) > 0) {
				if (quantity.compareTo(detail.getRemainingQuantity()) < 0) {
					detail.setRemainingQuantity(AppUtils.subtract(detail.getRemainingQuantity(), quantity));
					drilldowns.add(SCMDataConverter.convert(detail, quantity));
					quantity = BigDecimal.ZERO;
				} else {
					quantity = AppUtils.subtract(quantity, detail.getRemainingQuantity());
					drilldowns.add(SCMDataConverter.convert(detail, detail.getRemainingQuantity()));
					detail.setRemainingQuantity(BigDecimal.ZERO);
				}
				LOG.info("Req Qty : " + quantity + " Rem Qty :" + detail.getRemainingQuantity());
				gatepassManagementDao.update(detail, false);
			}
		}
		gatepassManagementDao.flush();
		item.getDrillDowns().addAll(drilldowns);
	}

	private void sortCollections(List<GatepassItemDrilldownDetail> drilldownDetails) {
		Collections.sort(drilldownDetails, new Comparator<GatepassItemDrilldownDetail>() {
			@Override
			public int compare(GatepassItemDrilldownDetail o1, GatepassItemDrilldownDetail o2) {
				if (o1.getExpiryDate().compareTo(o2.getExpiryDate()) == 0) {
					return o1.getId().compareTo(o2.getId());
				}
				return o1.getExpiryDate().compareTo(o2.getExpiryDate());
			}
		});
	}

	private void setGatepassStatusDetail(String from, String to, Integer createdBy, Integer gatepassId,
			boolean result) throws SumoException {
		GatepassStatusDetailData detailData = new GatepassStatusDetailData();
		detailData.setFromStatus(from);
		detailData.setToStatus(to);
		if (result) {
			detailData.setTransitionStatus(GatepassStatus.SUCCESS.name());
			detailData.setReason("SUCCESS");
		} else {
			detailData.setTransitionStatus(GatepassStatus.FAILURE.name());
			detailData.setReason("FAILURE");
		}
		detailData.setGatepassId(gatepassId);
		detailData.setUpdatedBy(createdBy);
		detailData.setUpdatedAt(AppUtils.getCurrentTimestamp());
		gatepassManagementDao.add(detailData, true);
	}

	private void setGatepassItemData(Gatepass gatepass, GatepassData gatepassData) {
		gatepass.getItemDatas().forEach(item -> {
			GatepassItemData itemData = new GatepassItemData();
			itemData.setSkuId(item.getSku().getId());
			itemData.setTransType(item.getTransType().name());
			itemData.setUom(item.getUom());
			itemData.setQuantity(item.getQuantity());
			if(gatepass.isAssetOrder()) {
			    itemData.setPrice(item.getPrice());
            }
			itemData.setTax(BigDecimal.ZERO);
			itemData.setCost(BigDecimal.ZERO);
			itemData.setAmount(BigDecimal.ZERO);

			itemData.setGatepassData(gatepassData);
			itemData.setCreatedBy(gatepassData.getCreatedBy());
			itemData.setCreatedAt(gatepassData.getCreatedAt());
			gatepassData.getItemDatas().add(itemData);
		});

	}

	private void setItemDrillDowns(Gatepass gatepass, GatepassData gatepassData) throws SumoException {
		HashMap<Integer, GatepassItemData> itemMap = getItemDataMap(gatepassData);

		for (GatepassItem item : gatepass.getItemDatas()) {
			List<GatepassItemDrilldownDetail> drilldownList = new ArrayList<>();
			GatepassItemData itemData = itemMap.get(item.getId());
			for (InventoryItemDrilldown drilldown : item.getDrillDowns()) {
				GatepassItemDrilldownDetail gpDrilldown = new GatepassItemDrilldownDetail();
				gpDrilldown.setItemData(itemData);
				gpDrilldown.setQuantity(drilldown.getQuantity());
				gpDrilldown.setRemainingQuantity(drilldown.getQuantity());
				gpDrilldown.setPrice(drilldown.getPrice());
				gpDrilldown.setAddTime(AppUtils.getCurrentTimestamp());
				gpDrilldown.setExpiryDate(drilldown.getExpiryDate());
				gatepassManagementDao.add(gpDrilldown, false);
				drilldownList.add(gpDrilldown);
			}
			itemData.setDrillDowns(drilldownList);
		}
		gatepassManagementDao.flush();

	}

	private HashMap<Integer, GatepassItemData> getItemDataMap(GatepassData gatepassData) {
		HashMap<Integer, GatepassItemData> itemMap = new HashMap<>();
		for (GatepassItemData item : gatepassData.getItemDatas()) {
			itemMap.put(item.getId(), item);
		}
		return itemMap;
	}

	private void setPriceTaxDetails(Gatepass gatepass, GatepassData gatepassData) throws GatepassException, SumoException {
		State fromState = masterDataCache.getUnit(gatepassData.getSendingUnit()).getLocation().getState();
		VendorDetail vendor = scmCache.getVendorDetail(gatepassData.getVendorId());
		Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendor,
				gatepassData.getDispatchLocationId());
		String toState = dispatchLocation.get().getAddress().getStateCode();
//		boolean applyTaxes = false;
//		if (gatepass.getItemDatas().size() > 0) {
//			applyTaxes = GatepassOperationType.REPAIR.equals(gatepass.getOperationType());
//		}
//		applyTaxes = !fromState.getCode().equals(toState) || applyTaxes;
//		LOG.info("fromState : " + fromState.getName() + " toState : " + toState + " applyTaxes : " + applyTaxes);
		if (dispatchLocation.isPresent()) {
			calculatePricesAndTaxes(fromState, toState, gatepass, gatepassData);
		} else {
			LOG.info("Dispatch Location not present!");
		}

	}

	private void calculatePricesAndTaxes(State fromState, String toStateCode, Gatepass gatepass,
										 GatepassData gatepassData) throws GatepassException, SumoException {
		HashMap<Integer, GatepassItemData> itemMap = getItemDataMap(gatepassData);
		for (GatepassItem gatepassItem : gatepass.getItemDatas()) {
			GatepassItemData itemData = itemMap.get(gatepassItem.getId());
			itemData.setPrice(gatepassItem.getPrice());
			itemData.setCost(AppUtils.multiply(itemData.getQuantity(), itemData.getPrice()));
				String code = scmCache
						.getProductDefinition(
								scmCache.getSkuDefinition(gatepassItem.getSku().getId()).getLinkedProduct().getId())
						.getTaxCode();
				if (code != null) {
					TaxData taxData = taxCache.getTaxData(fromState.getId(), code);
					if (fromState.getCode().equals(toStateCode)) {
						if (taxData.getState() != null && taxData.getState().getSgst().compareTo(BigDecimal.ZERO) > 0) {
							itemData.getTaxDetails()
									.add(setTaxes(itemData, getTaxPercentage("SGST", taxData), "SGST", "GST"));
						}
						if (taxData.getState() != null && taxData.getState().getCgst().compareTo(BigDecimal.ZERO) > 0) {
							itemData.getTaxDetails()
									.add(setTaxes(itemData, getTaxPercentage("CGST", taxData), "CGST", "GST"));
						}
					} else {
						if (taxData.getState() != null && taxData.getState().getIgst().compareTo(BigDecimal.ZERO) > 0) {
							itemData.getTaxDetails()
									.add(setTaxes(itemData, getTaxPercentage("IGST", taxData), "IGST", "GST"));
						}
					}

					if (taxData.getOthers() != null && taxData.getOthers().size() > 0) {
						for (AdditionalTax tax : taxData.getOthers()) {
							if (tax.getTax().compareTo(BigDecimal.ZERO) > 0) {
								itemData.getTaxDetails()
										.add(setTaxes(itemData, tax.getTax(), tax.getType(), tax.getType()));
							}
						}
					}
				}

			itemData.setAmount(AppUtils.add(itemData.getCost(), itemData.getTax()));
			gatepassData.setTotalTax(AppUtils.add(gatepassData.getTotalTax(), itemData.getTax()));
			gatepassData.setTotalCost(AppUtils.add(gatepassData.getTotalCost(), itemData.getCost()));
			BigDecimal totalCost = AppUtils.add(gatepassData.getTotalCost(), gatepassData.getTotalTax());
			totalCost = AppUtils.add(totalCost,
					(gatepassData.getAdditionalCharges() != null ? gatepassData.getAdditionalCharges()
							: BigDecimal.ZERO));
			String gatePassBypassIdsString = props.getGatePassBypassIds();
			List<Integer> gatePassBypassIds;
			if (Objects.nonNull(gatePassBypassIdsString) && !gatePassBypassIdsString.equalsIgnoreCase("")) {
				gatePassBypassIds = Arrays.stream(gatePassBypassIdsString.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
			} else {
				gatePassBypassIds = new ArrayList<>();
			}
			if (totalCost.compareTo(BigDecimal.valueOf(props.getGatepassLimit())) > 0 && !(gatePassBypassIds.contains(gatepassData.getCreatedBy()))) {
				throw new GatepassException("Gatepass Amount : " + totalCost + "can not be greater than " + props.getGatepassLimit());
			}
			gatepassManagementDao.update(itemData, false);
		}
		gatepassManagementDao.update(gatepassData, true);
	}

	private BigDecimal getTaxPercentage(String taxType, TaxData taxData) {
		BigDecimal percentage = BigDecimal.ZERO;
		switch (taxType) {
		case "IGST":
			percentage = percentage.add(taxData.getState().getIgst());
			break;
		case "SGST":
			percentage = percentage.add(taxData.getState().getSgst());
			break;
		case "CGST":
			percentage = percentage.add(taxData.getState().getCgst());
			break;
		}
		return percentage;
	}

	private GatepassTaxDetail setTaxes(GatepassItemData itemData, BigDecimal percentage, String code, String type) throws SumoException {
		GatepassTaxDetail taxDetail = new GatepassTaxDetail();
		taxDetail.setTaxType(type);
		taxDetail.setTaxCode(code);
		taxDetail.setTaxPercentage(percentage);
		BigDecimal tax = AppUtils.multiplyWithScale10(itemData.getCost(),
				AppUtils.divideWithScale10(percentage, new BigDecimal(100)));
		taxDetail.setTaxAmount(tax);
		taxDetail.setItemData(itemData);

		gatepassManagementDao.add(taxDetail, false);

		itemData.setTax(AppUtils.add(itemData.getTax(), tax));

		return taxDetail;
	}

	private void addLostItemsToWastage(List<GatepassItemData> itemDatas, Map<Integer, GatepassItem> itemMap)
			throws InventoryUpdateException, DataNotFoundException {
		Date date = AppUtils.getCurrentTimestamp();
		List<WastageEvent> wastageEventList = new ArrayList<>();

		WastageEvent event = new WastageEvent();
		event.setLinkedKettleIdType("GatepassId");
		event.setLinkedKettleId(itemDatas.get(0).getGatepassData().getId());
		event.setBusinessDate(SCMUtil.getDate(date));
		event.setGenerationTime(date);
		event.setStatus(StockEventStatus.SETTLED);
		event.setGeneratedBy(SCMServiceConstants.SYSTEM_USER);
		event.setUnitId(itemDatas.get(0).getGatepassData().getSendingUnit());

		for (GatepassItemData data : itemDatas) {
			createWastage(data, itemMap.get(data.getId()), event);
		}
		wastageEventList.add(event);
		if (wastageEventList.size() > 0) {
			addWastage(wastageEventList);
		}
	}

	private void createWastage(GatepassItemData data, GatepassItem item, WastageEvent event) {
		boolean isCafe = SCMUtil
				.isCafe(masterDataCache.getUnitBasicDetail(data.getGatepassData().getSendingUnit()).getCategory());
		WastageData wastage = new WastageData();
		SkuDefinition skuDef = scmCache.getSkuDefinitions().get(data.getSkuId());
		ProductDefinition productDef = scmCache.getProductDefinitions().get(skuDef.getLinkedProduct().getId());
		ProductBasicDetail product = SCMDataConverter.convert(productDef);
		wastage.setProduct(product);
		wastage.setQuantity(data.getQuantity());
		wastage.setPrice(data.getPrice());
		wastage.setComment("TOEP_LOST");
		wastage.setCost(SCMUtil.multiply(wastage.getQuantity(), data.getPrice()));
		wastage.setProductId(product.getProductId());
		if (!isCafe) {
			SkuBasicDetail sku = SCMDataConverter.convert(skuDef);
			wastage.setSku(sku);
			wastage.setSkuId(sku.getSkuId());
		}
		event.getItems().add(wastage);
	}

	@Override
	public StockManagementDao getDao() {
		return stockManagementDao;
	}

	@Override
	public PriceManagementDao getPriceDao() {
		return priceDao;
	}

	@Override
	public SCMCache getScmCache() {
		return scmCache;
	}

	@Override
	public InventoryService getInventoryService() {
		return inventoryService;
	}

	@Override
	public MasterDataCache getMasterDataCache() {
		return masterDataCache;
	}

	@Override
	public EnvProperties getProperties() {
		return props;
	}

}
