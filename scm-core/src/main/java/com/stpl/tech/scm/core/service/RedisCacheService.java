package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.domain.model.AssetDefinition;
import java.util.List;

public interface RedisCacheService {

    public List<AssetDefinition> getAssetsByUnitId(Integer unitId);

    public List<AssetDefinition> getAllAssets();

    public AssetDefinition getAssetByAssetId(Integer assetId);

    public void updateAssetToCache(AssetDefinition assetDefinition);

    public void reloadAssetCache();

    public void deleteAssetCache();

    public void initialAssetCacheLoad();


}
