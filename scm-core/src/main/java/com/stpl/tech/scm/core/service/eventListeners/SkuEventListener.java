package com.stpl.tech.scm.core.service.eventListeners;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.config.SpringContextHolder;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.model.SkuAttributeValueData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingMappingData;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.SkuAttributeValue;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.persistence.PostPersist;
import javax.persistence.PostUpdate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class SkuEventListener {

    private static final Logger LOG = LoggerFactory.getLogger(SkuEventListener.class);
    private static SCMCache scmCache;
    private static MasterDataCache masterDataCache;

    private static void getCache() {
        if (scmCache == null) {
            scmCache = SpringContextHolder.getBean(SCMCache.class);
        }
    }

    private static void getMasterCache() {
        if (masterDataCache == null) {
            masterDataCache = SpringContextHolder.getBean(MasterDataCache.class);
        }
    }

    @PostPersist
    public void handlePostPersist(Object entity) {
        getCache();
        if (entity instanceof SkuAttributeValueData attributeValueData) {
            skuAttributeValuePostPersistListener(attributeValueData);
        } else if (entity instanceof SkuPackagingMappingData packagingMappingData) {
            skuPackagingPostPersistListener(packagingMappingData);
        } else if (entity instanceof SkuDefinitionData skuDefinitionData) {
            skuDefinitionDataPostPersistListener(skuDefinitionData);
        } else {
            LOG.info("Invalid entity type for SKU EventListener for post persist");
        }
    }

    @PostUpdate
    public void handlePostUpdate(Object entity) {
        getCache();
        if (entity instanceof SkuAttributeValueData attributeValueData) {
            skuAttributeValuePostUpdateListener(attributeValueData);
        } else if (entity instanceof SkuPackagingMappingData packagingMappingData) {
            skuPackagingPostUpdateListener(packagingMappingData);
        } else if (entity instanceof SkuDefinitionData skuDefinitionData) {
            skuDefinitionDataPostUpdateListener(skuDefinitionData);
        } else {
            LOG.info("Invalid entity type for SKU EventListener for post update");
        }
    }



    public void skuDefinitionDataPostPersistListener(SkuDefinitionData sku) {
        SkuDefinition definition = SCMDataConverter.convert(sku, getCreatedBy(sku));
        scmCache.setSkuByProductId(definition.getLinkedProduct().getId(), definition);
    }

    public void skuDefinitionDataPostUpdateListener(SkuDefinitionData sku) {
        SkuDefinition definition = SCMDataConverter.convert(sku, getCreatedBy(sku));
        Integer productId = definition.getLinkedProduct().getId();
        scmCache.getSkusByProductId().compute(productId, (id, skus) -> {
            if (skus == null) skus = new ArrayList<>();
            skus.removeIf(skuDefinition -> skuDefinition.getSkuId().equals(definition.getSkuId()));
            return skus;
        });
        scmCache.setSkuByProductId(productId, definition);
    }

    private IdCodeName getCreatedBy(SkuDefinitionData sku) {
        getMasterCache();
        return SCMUtil.generateIdCodeName(sku.getCreatedBy(), "",
                masterDataCache.getEmployees().get(sku.getCreatedBy()));
    }

    public void skuAttributeValuePostPersistListener(SkuAttributeValueData attributeValueData) {
        for (Map.Entry<Integer, List<SkuDefinition>> entry : scmCache.getSkusByProductId().entrySet()) {
            for (SkuDefinition sku : entry.getValue()) {
                if (sku.getSkuId().equals(attributeValueData.getSkuId())) {
                    sku.getSkuAttributes().add(SCMDataConverter.convert(attributeValueData));
                    return;
                }
            }
        }
    }

    public void skuAttributeValuePostUpdateListener(SkuAttributeValueData attributeValueData) {
        for (Map.Entry<Integer, List<SkuDefinition>> entry : scmCache.getSkusByProductId().entrySet()) {
            for (SkuDefinition sku : entry.getValue()) {
                if (sku.getSkuId().equals(attributeValueData.getSkuId())) {
                    sku.getSkuAttributes().removeIf(attr -> attr.getAttributeId() == attributeValueData.getAttributeId());
                    sku.getSkuAttributes().add(SCMDataConverter.convert(attributeValueData));
                    return;
                }
            }
        }
    }

    public void skuPackagingPostPersistListener(SkuPackagingMappingData packagingMappingData) {
        for (Map.Entry<Integer, List<SkuDefinition>> entry : scmCache.getSkusByProductId().entrySet()) {
            for (SkuDefinition sku : entry.getValue()) {
                if (sku.getSkuId().equals(packagingMappingData.getSkuId())) {
                    sku.getSkuPackagings().add(SCMDataConverter.convert(packagingMappingData));
                    return;
                }
            }
        }
    }

    public void skuPackagingPostUpdateListener(SkuPackagingMappingData packagingMappingData) {
        for (Map.Entry<Integer, List<SkuDefinition>> entry : scmCache.getSkusByProductId().entrySet()) {
            for (SkuDefinition sku : entry.getValue()) {
                if (sku.getSkuId().equals(packagingMappingData.getSkuId())) {
                    sku.getSkuPackagings().removeIf(pack -> pack.getPackagingId() == packagingMappingData.getPackagingId());
                    sku.getSkuPackagings().add(SCMDataConverter.convert(packagingMappingData));
                    return;
                }
            }
        }
    }


}
