package com.stpl.tech.scm.core.service.impl;

import com.amazonaws.util.IOUtils;
import com.itextpdf.html2pdf.HtmlConverter;
import com.stpl.tech.kettle.core.service.impl.QRGenerationServiceImpl;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.cache.SCMStateTransitionCache;
import com.stpl.tech.scm.core.cache.SCMStateTransitionObject;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SCMError;
import com.stpl.tech.scm.core.exception.SalesPerformaInvoiceException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.SalesPerformaInvoiceService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.templates.B2BReturnSalesInvoiceTemplate;
import com.stpl.tech.scm.core.templates.B2BSalesInvoiceTemplate;
import com.stpl.tech.scm.core.templates.CorrectionCreditTemplate;
import com.stpl.tech.scm.core.templates.CorrectionDebitTemplate;
import com.stpl.tech.scm.core.templates.CreditNotePriceGapTemplate;
import com.stpl.tech.scm.core.templates.DebitNoteTemplate;
import com.stpl.tech.scm.core.templates.SpecializedOrderInvoice;
import com.stpl.tech.scm.core.util.AssetHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.dao.PurchaseOrderManagementDao;
import com.stpl.tech.scm.data.dao.SalesPerformaInvoiceDao;
import com.stpl.tech.scm.data.dao.TransferOrderManagementDao;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.InvoiceExcessQuantity;
import com.stpl.tech.scm.data.model.OutwardRegisterData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.SalesPerformaCorrectedInvoiceDetail;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCorrected;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCreditDebitNoteDetail;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCreditDebitNoteItemDetail;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceItemCorrected;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceItemData;
import com.stpl.tech.scm.data.model.SalesPerformaItemDrilldown;
import com.stpl.tech.scm.data.model.SalesPerformaItemTaxDetail;
import com.stpl.tech.scm.data.model.SalesPerformaStatusEventData;
import com.stpl.tech.scm.data.model.SpecializedOrderInvoiceData;
import com.stpl.tech.scm.domain.model.CorrectedSalesInvoiceDetails;
import com.stpl.tech.scm.domain.model.CorrectedSalesInvoiceItemDetails;
import com.stpl.tech.scm.domain.model.CreditDebitNoteDetail;
import com.stpl.tech.scm.domain.model.CreditDebitNoteItemDetail;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.EntityAssetMapping;
import com.stpl.tech.scm.domain.model.EntityAssetMappingType;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.GstInvoiceType;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GoodsReceivedItem;
import com.stpl.tech.scm.domain.model.GstSummary;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.InvoiceDocType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.OutwardRegister;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.SalesPerformaCorrectedType;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoiceItem;
import com.stpl.tech.scm.domain.model.SalesPerformaItemTax;
import com.stpl.tech.scm.domain.model.SalesPerformaStatus;
import com.stpl.tech.scm.domain.model.SalesPerformaType;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.TaxCategoryType;
import com.stpl.tech.scm.domain.model.TaxDetail;
import com.stpl.tech.scm.domain.model.TransitionStatus;
import com.stpl.tech.scm.domain.model.Vehicle;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.state.SCMTransitionData;
import com.stpl.tech.scm.domain.state.SCMTransitionStatus;
import com.stpl.tech.scm.notification.email.B2BReturnGenerationNotification;
import com.stpl.tech.scm.notification.email.CorrectionDetailsNotification;
import com.stpl.tech.scm.notification.email.CreditNoteGenerationNotification;
import com.stpl.tech.scm.notification.email.VendorDebitNoteNotification;
import com.stpl.tech.scm.notification.email.template.B2BReturnMailTemplate;
import com.stpl.tech.scm.notification.email.template.CorrectionDetailsTemplate;
import com.stpl.tech.scm.notification.email.template.CreditNoteGenerationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorDebitNoteNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.NumberToWord;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-07-2018.
 */
@Service
public class SalesPerformaInvoiceServiceImpl implements SalesPerformaInvoiceService {

    private static final Logger LOG = LoggerFactory.getLogger(SalesPerformaInvoiceServiceImpl.class);

    @Autowired
    private SCMNotificationService notificationService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private PurchaseOrderManagementDao purchaseOrderManagementDao;

    @Autowired
    private SalesPerformaInvoiceDao invoiceDao;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private EnvProperties props;

    @Autowired
    private PriceManagementDao pricingDao;

    @Autowired
    private SCMAssetManagementService scmAssetManagementService;

    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;

    @Autowired
    private TransferOrderManagementDao transferOrderManagementDao;

    @Autowired
    private SalesPerformaInvoiceService salesPerformaInvoiceService;

    @Autowired
    private StockManagementService stockManagementService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SalesPerformaInvoice createInvoice(SalesPerformaInvoice invoice) throws SalesPerformaInvoiceException, DataNotFoundException, InventoryUpdateException, SumoException {
        validateAndUpdateInvoice(invoice);
        try {
            SalesPerformaDetailData detailData = createInvoiceDetail(invoice);
            List<SalesPerformaInvoiceItemData> itemDataList = createInvoiceItemsAndTaxes(invoice.getItems(), detailData);
            detailData.setInvoiceItems(itemDataList);
//            boolean ewayRequired = isEwayBillRequired(detailData);
            Unit transferUnit = masterDataCache.getUnit(detailData.getSendingUnit());
            VendorDetail toVendor = scmCache.getVendorDetail(detailData.getVendor());

            SalesPerformaInvoice finalOutput = SCMDataConverter.convert(detailData, masterDataCache, scmCache);
            checkForCurrentPrice(finalOutput);

            SalesPerformaStatus toStatus = SalesPerformaStatus.PERFORMA_GENERATED;
            //TODO B2B invoice use this code
            if (!InvoiceDocType.SYSTEM_INVOICE.equals(invoice.getInvoiceType()) && !invoice.getInvoiceType().equals(InvoiceDocType.DELIVERY_CHALLAN)) {
                toStatus = SalesPerformaStatus.APPROVED;
            }
            if(Objects.nonNull(invoice.getType()) &&
                    (invoice.getType().equals(SalesPerformaType.RETURN_TO_VENDOR) ||
                    invoice.getType().equals(SalesPerformaType.SCRAP))){
                toStatus = SalesPerformaStatus.PENDING_APPROVAL_L1;
            }
//           if (!ewayRequired && !InvoiceDocType.SYSTEM_INVOICE.equals(invoice.getInvoiceType()) && !invoice.getInvoiceType().equals(InvoiceDocType.DELIVERY_CHALLAN)) {
//                toStatus = SalesPerformaStatus.APPROVED;
//            }
//            //if ewayRequired is false and invoice type is DELIVERY_CHALLAN set the status to PENDING_DISPATCH
//            if(!ewayRequired && (invoice.getInvoiceType().equals(InvoiceDocType.DELIVERY_CHALLAN) ||
//                    InvoiceDocType.SYSTEM_INVOICE.equals(invoice.getInvoiceType()))) {
//                toStatus = SalesPerformaStatus.PENDING_DISPATCH;
//            }
            if(detailData.getType().equals(SalesPerformaType.B2B_RETURN.name())){
                toStatus = SalesPerformaStatus.APPROVED;
                detailData.setGeneratedCreditNoteId(generateCreditNoteDocId(transferOrderManagementDao.getNextStateInvoiceId(7, FileType.CREDIT_NOTE.value())));
                sendB2BReturnMail(invoice);
            }

            createInvoiceStatusEvent(detailData, detailData.getCreatedBy(), SalesPerformaStatus.INITIATED, toStatus);
            //update status if everything goes the way as intended
            detailData.setStatus(toStatus.name());
            invoiceDao.update(detailData, true);

            finalOutput = SCMDataConverter.convert(detailData, masterDataCache, scmCache);

            if (toStatus.equals(SalesPerformaStatus.PENDING_DISPATCH)) {
                notificationService.sendInvoiceNotification(detailData, detailData.getCreatedBy());
            }

            if(!invoice.getType().equals(SalesPerformaType.RETURN_TO_VENDOR) &&
                    !invoice.getType().equals(SalesPerformaType.B2B_RETURN)){
                detailData.setUploadDocId(generateTransportDocId(finalOutput, transferUnit, toVendor));
            }
            invoiceDao.update(detailData, true);
//            finalOutput = pricingDao.reduceConsumable(finalOutput, false);
//            finalOutput = updatePrices(finalOutput, detailData);
            if (invoice.isAssetOrder()) {
                /*
                    Populate data and save EntityAssetMapping
                */
                int counter = 0;
                for (SalesPerformaInvoiceItemData salesPerformaInvoiceItemData : detailData.getInvoiceItems()) {
                    LOG.info(salesPerformaInvoiceItemData.toString());
                    List<EntityAssetMapping> entityAssetMappings = invoice.getItems().get(counter).getAssociatedAssetMappings();
                    List<EntityAssetMapping> entityAssetMappingList = setEntityAssetMappingData(detailData, salesPerformaInvoiceItemData, entityAssetMappings);
                    entityAssetMappingList = scmAssetManagementService.createEntityAssetMapping(entityAssetMappingList);
                    SalesPerformaInvoiceItem salesPerformaInvoiceItem = finalOutput.getItems().get(counter);
                    salesPerformaInvoiceItem.setAssociatedAssetMappings(entityAssetMappingList);
                    counter++;
                }
            }
//            if (SalesPerformaType.B2B_RETURN.equals(finalOutput.getType())) {
//                return markInvoiceReadyToDispatch(finalOutput.getId(),finalOutput.getCreatedBy().getId());
//            }
            return finalOutput;
        } catch (Exception e) {
            LOG.error("Error while creating invoice for sales performa invoice ::::: ", e);
            throw new SalesPerformaInvoiceException(e.getMessage(), e.getCause());
        }
    }

    private void checkForCurrentPrice(SalesPerformaInvoice invoice) throws DataNotFoundException, InventoryUpdateException {
        if (!invoice.isAssetOrder()) {
            invoice = pricingDao.checkConsumableData(invoice);
        }
    }

    public void sendB2BReturnMail(SalesPerformaInvoice invoice) throws FileNotFoundException {
        try {
            B2BReturnMailTemplate template = new B2BReturnMailTemplate(invoice, props);
            B2BReturnGenerationNotification notification = new B2BReturnGenerationNotification(props.getEnvType(), template, invoice);
            notification.sendEmail();
        } catch (Exception e) {
            LOG.error("Error sending mail for b2b invoice generation : {} ", invoice.getId());
            LOG.error(e.getMessage());
        }
    }

    private List<EntityAssetMapping> setEntityAssetMappingData(SalesPerformaDetailData salesPerformaDetailData, SalesPerformaInvoiceItemData salesPerformaInvoiceItemData,
                                                               List<EntityAssetMapping> entityAssetMappings) {
        for (EntityAssetMapping mapping : entityAssetMappings) {
            mapping.setEntityCategory(salesPerformaDetailData.getType());
            mapping.setEntityId(salesPerformaDetailData.getInvoiceId());
            mapping.setEntitySubId(salesPerformaInvoiceItemData.getItemId());
            mapping.setEntityType(EntityAssetMappingType.INVOICE.value());
        }
        return entityAssetMappings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, String poId, Integer userId,
                                         MultipartFile file) {
        String fileName =  poId + "VENDOR_PO_" + "." + mimeType.extension();
        try {
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "purchaseOrders", fileName, file);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = purchaseOrderManagementDao.add(SCMDataConverter.convert(documentDetail), true);
            if (data.getDocumentId() != null) {
                return SCMDataConverter.convert(data);
            }
        } catch (Exception e) {
            LOG.error("Encountered error while uploading document", e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadCancelInvoiceDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId,
                                         MultipartFile file) {
        String fileName = "INVOICE_CANCEL" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "."
            + mimeType.name().toLowerCase();
        try {
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_REQUESTS/CANCEL", fileName, file);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = invoiceDao.add(SCMDataConverter.convert(documentDetail), true);
            if (data.getDocumentId() != null) {
                return SCMDataConverter.convert(data);
            }
        } catch (Exception e) {
            LOG.error("Encountered error while uploading document", e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean saveEntry(OutwardRegister outWardRegister) throws IOException, SumoException {
        invoiceDao.add(SCMDataConverter.convertOutwardRegister(outWardRegister), true);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OutwardRegister> getEntries(Integer sendingUnit, Date startDate, Date endDate, String businessType) throws IOException, SumoException {
        List<OutwardRegisterData> outwardRegisterList = invoiceDao.getOutWardRegisterEntry(sendingUnit, startDate, endDate, businessType);
        List<OutwardRegister> outwardRegisterEntriesList = new ArrayList<>();
        if (outwardRegisterList != null && !outwardRegisterList.isEmpty()) {
            for (OutwardRegisterData entry : outwardRegisterList) {
                OutwardRegister outwardRegisterEntry = SCMDataConverter.convertOutwardRegisterData(entry);
                outwardRegisterEntriesList.add(outwardRegisterEntry);
            }
        }
        return outwardRegisterEntriesList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SalesPerformaInvoice> viewInvoices(Integer sendingUnit, Date startDate, Date endDate, List<String> status, Integer vendorId, Integer dispatchId, Boolean fetchPending, String businessType , Boolean raiseCreditNote) {
        String trnsType = null;
        if(Objects.nonNull(raiseCreditNote) && raiseCreditNote){
            status = new ArrayList<>();
            status.add(SalesPerformaStatus.DELIVERED.name());
            status.add(SalesPerformaStatus.DELIVERED_WITH_CORRECTION.name());
            trnsType = SalesPerformaType.B2B_SALES.name();

        }
        List<SalesPerformaDetailData> detailList = invoiceDao.getInvoices(sendingUnit, startDate, endDate, status, vendorId, dispatchId, businessType ,trnsType);
        List<SalesPerformaInvoice> invoices = new ArrayList<>();
        if (detailList != null && !detailList.isEmpty()) {
            for (SalesPerformaDetailData detail : detailList) {
                SalesPerformaInvoice salesPerformaInvoice = SCMDataConverter.convert(detail, masterDataCache, scmCache);
                if (salesPerformaInvoice.isAssetOrder()) {
                    /*
                        Add associated Assets
                     */
                    for (SalesPerformaInvoiceItem salesPerformaInvoiceItem : salesPerformaInvoice.getItems()) {
                        List<EntityAssetMapping> entityAssetMappings = scmAssetManagementService.getAssociatedEntityAssetMapping(detail.getInvoiceId(), salesPerformaInvoiceItem.getId(), EntityAssetMappingType.INVOICE.value());
                        salesPerformaInvoiceItem.setAssociatedAssetMappings(entityAssetMappings);
                    }
                }
                List<SalesPerformaInvoiceCorrected> revisedDetails = invoiceDao.getCorrectedInvoiceDetails(salesPerformaInvoice.getId(),null);

                if(Objects.nonNull(revisedDetails)){
                    for(SalesPerformaInvoiceCorrected revisedDetail : revisedDetails){
                        if(revisedDetail.getType().equals(SalesPerformaCorrectedType.CREDIT_NOTE.value())){
                            CorrectedSalesInvoiceDetails creditNoteDtl =  salesPerformaInvoiceService.getCorrectedInvoiceDetails(detail.getInvoiceId(),SalesPerformaCorrectedType.CREDIT_NOTE.value()).get(0);
                            salesPerformaInvoice.setCorrectionCreditNoteDetail(creditNoteDtl);
                        } else if(revisedDetail.getType().equals(SalesPerformaCorrectedType.DEBIT_NOTE.value())){
                            CorrectedSalesInvoiceDetails debitNoteDtl = salesPerformaInvoiceService.getCorrectedInvoiceDetails(detail.getInvoiceId(),SalesPerformaCorrectedType.DEBIT_NOTE.value()).get(0);
                            salesPerformaInvoice.setCorrectionDebitNoteDetail(debitNoteDtl);
                        }
                    }
                }
                invoices.add(salesPerformaInvoice);
            }
        }

        if (Objects.isNull(fetchPending)) {
            return invoices;
        }
        if(Boolean.FALSE.equals(fetchPending))
        {
            return filterInvoices(invoices, false);
        }
        if (Boolean.TRUE.equals(fetchPending)) {
            return filterInvoices(invoices, true);
        }
        return invoices;

    }

    private List<SalesPerformaInvoice> filterInvoices(List<SalesPerformaInvoice> invoices, Boolean showAll) {
        if (Objects.nonNull(invoices)) {
            List<Integer> invoiceIdList = new ArrayList<>();
            for (SalesPerformaInvoice i : invoices) {
                invoiceIdList.add(i.getId());
            }
            List<Integer> submittedInvoices = invoiceDao.getSubmittedInvoices(invoiceIdList);
            List<Integer> finalSubmittedInvoices = submittedInvoices;
            if (Boolean.valueOf(showAll).equals(false)) {
                return invoices.stream().filter(invoice -> finalSubmittedInvoices.contains(invoice.getId())).collect(Collectors.toList());
            }
            if (Boolean.valueOf(showAll).equals(true)) {
                return invoices.stream().filter(invoice -> !finalSubmittedInvoices.contains(invoice.getId())).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SalesPerformaInvoice approveInvoice(SalesPerformaInvoice invoice, Integer userId) throws SalesPerformaInvoiceException, SumoException, FileNotFoundException {
        SalesPerformaDetailData detailData = invoiceDao.find(SalesPerformaDetailData.class, invoice.getId());
        //TODO B2B invoicing use this code
        if(detailData.getStatus().equals(SalesPerformaStatus.PENDING_APPROVAL_L1.name())){
            createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.PENDING_APPROVAL_L2);
            detailData.setNeedsApproval(AppConstants.YES);
            detailData.setStatus(SalesPerformaStatus.PENDING_APPROVAL_L2.name());
        } else if(detailData.getStatus().equals(SalesPerformaStatus.PENDING_APPROVAL_L2.name())){
            createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.APPROVED);
            detailData.setNeedsApproval(AppConstants.NO);
            detailData.setStatus(SalesPerformaStatus.APPROVED.name());
            if(detailData.getType().equals(SalesPerformaType.RETURN_TO_VENDOR.name())){
                detailData.setGeneratedDebitNoteId(generateDebitNoteDocId(transferOrderManagementDao.getNextStateInvoiceId(7, FileType.DEBIT_NOTE.value())));
            }
        }else if(detailData.getStatus().equals(SalesPerformaStatus.CORRECTION_APPROVAL_L1.name())){
            createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.CORRECTION_APPROVAL_L2);
            updateCorrectionDetailsStatus(detailData,SalesPerformaStatus.CORRECTION_APPROVAL_L2.value());
            detailData.setNeedsApproval(AppConstants.YES);
            detailData.setStatus(SalesPerformaStatus.CORRECTION_APPROVAL_L2.name());
        }else if(detailData.getStatus().equals(SalesPerformaStatus.CORRECTION_APPROVAL_L2.name())){
            createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.CORRECTION_APPROVED);
            detailData.setNeedsApproval(AppConstants.NO);
            detailData.setStatus(SalesPerformaStatus.CORRECTION_APPROVED.name());
            updateCorrectionDetailsStatus(detailData,SalesPerformaStatus.CORRECTION_APPROVED.value());
            sendMail(detailData);
        }else {
            createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.APPROVED);
            detailData.setStatus(SalesPerformaStatus.APPROVED.name());
            detailData.setNeedsApproval(AppConstants.NO);
        }
        invoiceDao.update(detailData, true);
        return SCMDataConverter.convert(detailData, masterDataCache, scmCache);
        /*createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.PENDING_DISPATCH);
        detailData.setStatus(SalesPerformaStatus.PENDING_DISPATCH.name());
        invoiceDao.update(detailData, true);
        return SCMDataConverter.convert(detailData, masterDataCache, scmCache);*/
    }

    public void updateCorrectionDetailsStatus(SalesPerformaDetailData salesPerformaDetailData,String status) throws SalesPerformaInvoiceException {

        SalesPerformaInvoice salesPerformaInvoice = SCMDataConverter.convert(salesPerformaDetailData, masterDataCache, scmCache);
        List<SalesPerformaInvoiceCorrected> detailData = invoiceDao.getCorrectedInvoiceDetails(salesPerformaDetailData.getInvoiceId(),null);

        if(Objects.nonNull(detailData)){
            for(SalesPerformaInvoiceCorrected detail : detailData){
                detail.setInvoiceStatus(status);
                if(status.equals(SalesPerformaStatus.CORRECTION_APPROVED.name())){
                    SalesPerformaInvoice newInvoiceObj = updateInvoiceForInventoryChange(detail,salesPerformaInvoice);
                    if(detail.getType().equals(SalesPerformaCorrectedType.DEBIT_NOTE.value())){

                        detail.setGeneratedDebitNoteId(generateDebitNoteDocId(transferOrderManagementDao.getNextStateInvoiceId(7, FileType.DEBIT_NOTE.value())));
                            try {
                                salesPerformaInvoice = pricingDao.reduceConsumable(newInvoiceObj, false);
                            } catch (Exception e) {
                                LOG.error("Error while updating inventory for sales performa invoice ::::: ", e);
                                throw new SalesPerformaInvoiceException(e.getMessage(), e.getCause());
                            }
                    }else if (detail.getType().equals(SalesPerformaCorrectedType.CREDIT_NOTE.value())){
                        detail.setGeneratedCreditNoteId(generateCreditNoteDocId(transferOrderManagementDao.getNextStateInvoiceId(7, FileType.CREDIT_NOTE.value())));
                        try {
                                pricingDao.addReceiving(newInvoiceObj, false);
                            } catch (Exception e) {
                                LOG.error("Error while updating inventory for sales performa invoice ::::: ", e);
                                throw new SalesPerformaInvoiceException(e.getMessage(), e.getCause());
                            }
                    }
                }
                invoiceDao.update(detail,false);
            }
        }

    }


    public SalesPerformaInvoice updateInvoiceForInventoryChange(SalesPerformaInvoiceCorrected correctedDetail,SalesPerformaInvoice invoice){

        SalesPerformaInvoice inv = SCMUtil.clone(invoice, SalesPerformaInvoice.class);

        for (SalesPerformaInvoiceItem item : inv.getItems()) {
            boolean itemPresent = false;

            for (SalesPerformaInvoiceItemCorrected correctedItem : correctedDetail.getSalesPerformaCorrectedItems()) {
                if (correctedItem.getSkuId().equals(item.getSku().getId())) {
                    itemPresent = true;
                    List<InventoryItemDrilldown> drillDowns = new ArrayList<>();
                    InventoryItemDrilldown drillDown = item.getDrillDowns().get(0);
                    drillDown.setQuantity(SCMUtil.multiply(correctedItem.getCorrectedPkgQty(),item.getRatio()));
                    drillDown.setPrice(SCMUtil.divide(correctedItem.getCorrectedPrice(),item.getRatio()));
                    drillDowns.add(drillDown);
                    item.setDrillDowns(drillDowns);
                }
            }
            if(!itemPresent){
                item.setDrillDowns(null);
            }

        }
        return inv;
    }

    public void sendMail(SalesPerformaDetailData salesPerformaDetailData) throws FileNotFoundException {
        try{
            SalesPerformaCorrectedInvoiceDetail correctedSalesInvoiceDetails = invoiceDao.find(SalesPerformaCorrectedInvoiceDetail.class,salesPerformaDetailData.getInvoiceId());
            CorrectionDetailsTemplate template = new CorrectionDetailsTemplate(salesPerformaDetailData,props,correctedSalesInvoiceDetails);
            CorrectionDetailsNotification notification = new CorrectionDetailsNotification(props.getEnvType(),template,salesPerformaDetailData);
            notification.sendEmail();
        }catch (Exception e){
            LOG.error("Error sending mail for correction details approval : {} ",salesPerformaDetailData.getInvoiceId());
            LOG.error(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SalesPerformaInvoice rejectInvoice(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException {
        SalesPerformaDetailData detailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);

        if(detailData.getStatus().equals(SalesPerformaStatus.CORRECTION_APPROVAL_L1.name())) {
            createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.DELIVERED);
            detailData.setNeedsApproval(AppConstants.NO);
            detailData.setStatus(SalesPerformaStatus.DELIVERED.name());
            updateCorrectionDetailsStatus(detailData,SalesPerformaStatus.DELIVERED.value());

        }else if(detailData.getStatus().equals(SalesPerformaStatus.CORRECTION_APPROVAL_L2.name())) {
            createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.DELIVERED);
            detailData.setNeedsApproval(AppConstants.NO);
            detailData.setStatus(SalesPerformaStatus.DELIVERED.name());
            updateCorrectionDetailsStatus(detailData,SalesPerformaStatus.DELIVERED.value());

        }else {
            createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.REJECTED);
            detailData.setStatus(SalesPerformaStatus.REJECTED.name());
            detailData.setNeedsApproval(AppConstants.NO);
        }
        invoiceDao.update(detailData, true);
        return SCMDataConverter.convert(detailData, masterDataCache, scmCache);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SalesPerformaInvoice markInvoiceReadyToDispatch(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException {
        SalesPerformaDetailData detailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        if (Objects.isNull(detailData.getInvoiceDocumentId()) && SalesPerformaType.SCRAP.name().equalsIgnoreCase(detailData.getType())) {
            throw new SumoException("Please upload invoice first");
        }
        if (Objects.isNull(detailData.getDebitNoteDocId())) {
            if (SalesPerformaType.RETURN_TO_VENDOR.name().equalsIgnoreCase(detailData.getType())) {
                throw new SumoException("Please generate debit note");
            }
        }
        if (Objects.isNull(detailData.getCreditNoteDocId())) {
            if (SalesPerformaType.B2B_RETURN.name().equalsIgnoreCase(detailData.getType())) {
                throw new SumoException("Please generate credit note");
            }
        }
        if (Objects.isNull(detailData.getInvoiceDocumentId())) {
            if (SalesPerformaType.B2B_SALES.name().equalsIgnoreCase(detailData.getType()) && !GstInvoiceType.UNREGISTERED.equals(detailData.getGstInvoiceType())) {
                throw new SumoException("Please generate invoice");
            }
        }
        if (detailData.getIrnNo() == null) {
            if (((SalesPerformaType.B2B_SALES.name().equalsIgnoreCase(detailData.getType())) ||
                    (SalesPerformaType.ECOM.name().equalsIgnoreCase(detailData.getType())) ||
                    (SalesPerformaType.RETURN_TO_VENDOR.name().equalsIgnoreCase(detailData.getType())) ||
                    (SalesPerformaType.B2B_RETURN.name().equalsIgnoreCase(detailData.getType()))) && !GstInvoiceType.UNREGISTERED.equals(detailData.getGstInvoiceType()) ) {
                throw new SumoException("Please upload E-Invoice Sheet");
            }
        }

        createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.PENDING_DISPATCH);
        detailData.setStatus(SalesPerformaStatus.PENDING_DISPATCH.name());
        invoiceDao.update(detailData, true);
        return SCMDataConverter.convert(detailData, masterDataCache, scmCache);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SalesPerformaInvoice> getClosedInvoicesForVendor(Integer vendorId, Integer dispatchId, Integer sendingUnit) {
        List<String> closedStatus=new ArrayList<>();
        closedStatus.add("CLOSED");
        List<SalesPerformaDetailData> invoices = invoiceDao.getInvoices(sendingUnit, null, null, closedStatus, vendorId, dispatchId, null,null);
        List<SalesPerformaInvoice> invoiceList = new ArrayList<>();
        if (invoices != null) {
            for (SalesPerformaDetailData invoice : invoices) {
                invoiceList.add(SCMDataConverter.convert(invoice, masterDataCache, scmCache));
            }
        }
        return invoiceList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SalesPerformaInvoice cancelInvoice(Integer invoiceId, Integer userId, Integer docId) throws SalesPerformaInvoiceException, SumoException, InventoryUpdateException {
        SalesPerformaDetailData detailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        String status = SalesPerformaStatus.CANCELLED.name();
        if(detailData.getStatus().equals(SalesPerformaStatus.PENDING_DISPATCH.name()) ||
                detailData.getStatus().equals(SalesPerformaStatus.DELIVERED.name() ) ||
                detailData.getStatus().equals(SalesPerformaStatus.CLOSED.name())){
            status = SalesPerformaStatus.PENDING_CANCEL_APPROVAL.name();
        }
        createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.valueOf(status));
        SalesPerformaInvoice invoice = SCMDataConverter.convert(detailData, masterDataCache, scmCache);
//        RECEIVING CALL TO BE ADDED WHEN REQUIRED
//        if (detailData.getStatus().equals(SalesPerformaStatus.CLOSED.name())) {
//            pricingDao.addReceiving(invoice, true);
//        }
        if(!status.equals(SalesPerformaStatus.PENDING_CANCEL_APPROVAL.name())) {
            detailData.setCancelledAt(SCMUtil.getCurrentTimestamp());
            detailData.setCancelledBy(userId);
            detailData.setNeedsApproval(AppConstants.NO);
            detailData.setCancelDocId(docId != null ? docId : null);
        }
        detailData.setStatus(status);
        detailData = invoiceDao.update(detailData, true);
        invoice = SCMDataConverter.convert(detailData, masterDataCache, scmCache);
        return invoice;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, String> uploadInvoice(MimeType mimeType, Integer invoiceId, File file, Integer userId) throws SalesPerformaInvoiceException, SumoException {
        Map<String, String> response = new HashMap<>();
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        //TODO B2B invoicing use this code
//        boolean ewayRequired = isEwayBillRequired(performa);
//        if (ewayRequired && performa.getEwayDocumentId() == null) {
//            throw new SumoException("Please upload eway bill first!");
//        }
        String fileName = "INVOICE_" + invoiceId + "." + mimeType.extension();
        FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_REQUESTS/INVOICE", fileName, file, true);
        DocumentDetailData documentDetailData = saveToDocumentDetail(fileDetail, userId, fileName, InvoiceDocType.valueOf(performa.getInvoiceType()), FileType.SALES_INVOICE, invoiceId);
        performa.setInvoiceDocUrl(fileDetail.getUrl());
        performa.setInvoiceDocumentId(documentDetailData.getDocumentId());
        invoiceDao.update(performa, true);
        response.put("invoice", fileDetail.getUrl());
        response.put("notification", String.valueOf(checkForAllUploadsAndSendNotification(performa, userId)));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, String> uploadSpecializedOrderInvoice(MimeType mimeType, SpecializedOrderInvoiceData invoice, File file,
                                                             Integer userId) throws SalesPerformaInvoiceException, SumoException {
        Map<String, String> response = new HashMap<>();

        String fileName = "SPECIALIZED_ORDER_INVOICE_" + invoice.getInvoiceId() + "." + mimeType.extension();
        FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "SPECIALIZED_INVOICE_REQUESTS/INVOICE", fileName, file, true);
        DocumentDetailData documentDetailData = saveToDocumentDetail(fileDetail, userId, fileName,
                InvoiceDocType.SYSTEM_INVOICE, FileType.OTHERS, invoice.getSpecializedOrderInvoiceId());
        invoice.setInvoiceUrl(fileDetail.getUrl());
        invoice.setDocumentId(documentDetailData.getDocumentId());
        invoiceDao.update(invoice, true);
        response.put("invoice", fileDetail.getUrl());
        //response.put("notification", String.valueOf(checkForAllUploadsAndSendNotification(performa, userId)));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, String> uploadEwayBill(MimeType mimeType, Integer invoiceId, MultipartFile file, Integer userId) throws SalesPerformaInvoiceException, SumoException {
        Map<String, String> response = new HashMap<>();
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        String fileName = "EWAY_" + invoiceId + "." + mimeType.extension();
        FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_REQUESTS/EWAY", fileName, file, true);
        DocumentDetailData documentDetailData = saveToDocumentDetail(fileDetail, userId, fileName, InvoiceDocType.valueOf(performa.getInvoiceType()), FileType.SALES_INVOICE, invoiceId);
        performa.seteWayDocUrl(fileDetail.getUrl());
        performa.setEwayDocumentId(documentDetailData.getDocumentId());
        invoiceDao.update(performa, true);
        response.put("invoice", fileDetail.getUrl());
        response.put("notification", String.valueOf(checkForAllUploadsAndSendNotification(performa, userId)));
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, String> uploadDeliveredDocument(MimeType mimeType, Integer invoiceId, MultipartFile file, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException {
        Map<String, String> response = new HashMap<>();
        SalesPerformaDetailData performaDetailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        createInvoiceStatusEvent(performaDetailData, userId, SalesPerformaStatus.valueOf(performaDetailData.getStatus()), SalesPerformaStatus.DELIVERED);
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        return saveDocumentForSalesPerforma(mimeType, invoiceId, file, userId, response, performa);
    }

    @Autowired
    private EnvProperties envProperties;

    private Map<String, String> saveDocumentForSalesPerforma(MimeType mimeType, Integer invoiceId, MultipartFile file, Integer userId, Map<String, String> response, SalesPerformaDetailData performa) throws SumoException, IOException, TemplateRenderingException, SalesPerformaInvoiceException {
        String fileName = "DELIVER_" + invoiceId + "." + mimeType.extension();

        FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_REQUESTS/DELIVERED", fileName, file, true);
        DocumentDetailData documentDetailData = saveToDocumentDetail(fileDetail, userId, fileName, InvoiceDocType.valueOf(performa.getInvoiceType()), FileType.SALES_INVOICE, invoiceId);
        performa.setDeliveredDocUrl(fileDetail.getUrl());
        performa.setDeliveredDocumentId(documentDetailData.getDocumentId());
        performa.setStatus(SalesPerformaStatus.DELIVERED.value());
        invoiceDao.update(performa, true);
        response.put("invoice", fileDetail.getUrl());
        return response;

    }


        @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public URL downloadInvoice(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException {
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        if (performa != null && performa.getInvoiceDocumentId() != null) {
            DocumentDetailData document = invoiceDao.find(DocumentDetailData.class, performa.getInvoiceDocumentId());
            return fileArchiveService.getSignedUrl(document.getS3Bucket(), document.getS3Key());
        }
        return ( performa != null && performa.getInvoiceDocUrl() != null ) ? new URL(performa.getInvoiceDocUrl()) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public URL downloadEway(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException {
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        if (performa != null && performa.getEwayDocumentId() != null) {
            DocumentDetailData document = invoiceDao.find(DocumentDetailData.class, performa.getEwayDocumentId());
            return fileArchiveService.getSignedUrl(document.getS3Bucket(), document.getS3Key());
        }
        return performa != null ? new URL(performa.geteWayDocUrl()) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public URL downloadDeliverDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException {
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        if (performa != null && performa.getDeliveredDocumentId() != null) {
            DocumentDetailData document = invoiceDao.find(DocumentDetailData.class, performa.getDeliveredDocumentId());
            return fileArchiveService.getSignedUrl(document.getS3Bucket(), document.getS3Key());
        }
        return performa != null ? new URL(performa.getDeliveredDocUrl()) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public URL downloadPoDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException {
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        if (performa != null && performa.getPoDocumentId() != null) {
            DocumentDetailData document = invoiceDao.find(DocumentDetailData.class, performa.getPoDocumentId());
            return fileArchiveService.getSignedUrl(document.getS3Bucket(), document.getS3Key());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public URL downloadCancelDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException {
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        if (performa != null && performa.getCancelDocId() != null) {
            DocumentDetailData document = invoiceDao.find(DocumentDetailData.class, performa.getCancelDocId());
            return fileArchiveService.getSignedUrl(document.getS3Bucket(), document.getS3Key());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SalesPerformaInvoice dispatchInvoice(Integer invoiceId, Integer userId,Date dateOfDelivery) throws SalesPerformaInvoiceException, SumoException, ParseException {
        SalesPerformaDetailData performaDetailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        if (performaDetailData != null) {
            if(SalesPerformaType.B2B_RETURN.name().equals(performaDetailData.getType())){
                createInvoiceStatusEvent(performaDetailData, userId, SalesPerformaStatus.valueOf(performaDetailData.getStatus()), SalesPerformaStatus.DELIVERED);
                performaDetailData.setStatus(SalesPerformaStatus.DELIVERED.name());
            }else {
                createInvoiceStatusEvent(performaDetailData, userId, SalesPerformaStatus.valueOf(performaDetailData.getStatus()), SalesPerformaStatus.CLOSED);
                performaDetailData.setStatus(SalesPerformaStatus.CLOSED.name());
            }
            performaDetailData.setDateOfDelivery(dateOfDelivery);
            performaDetailData = invoiceDao.update(performaDetailData, true);
            SalesPerformaInvoice salesPerformaInvoice = SCMDataConverter.convert(performaDetailData, masterDataCache, scmCache);
            if(!SalesPerformaType.B2B_RETURN.name().equals(performaDetailData.getType())){
                stockManagementService.verifyInventoryForKeys(salesPerformaInvoice, false);
            }
            /*
                If Invoice is for Fixed Assets than manage asset location and depreciation
             */

            if (salesPerformaInvoice.isAssetOrder()) {
                // updating the current amount and Current Price with the Asset Current Price For inventory Movement Report
                updateAssetPrices(salesPerformaInvoice);
                scmAssetManagementService.transferAssetAgainstInvoice(salesPerformaInvoice);
            } else {
                if (SalesPerformaType.B2B_RETURN.name().equals(performaDetailData.getType())) {
                    try {
                        // getting the IS_LATEST price and setting that as drilldown Price
                        Map<Integer, CostDetailData> currentPricesMap = new HashMap<>();
                        setDrillDowns(salesPerformaInvoice, currentPricesMap);
                        pricingDao.addReceiving(salesPerformaInvoice, false);
                        updateCurrentPriceForReturns(currentPricesMap, salesPerformaInvoice);
                    } catch (SumoException e) {
                        throw e;
                    } catch (Exception e) {
                        LOG.error("Error while updating inventory for sales performa invoice ::::: ", e);
                        throw new SalesPerformaInvoiceException(e.getMessage(), e.getCause());
                    }
                } else {
                    try {
                        salesPerformaInvoice = pricingDao.reduceConsumable(salesPerformaInvoice, false);
                        updatePrices(salesPerformaInvoice, performaDetailData);
                    } catch (SumoException e) {
                        throw e;
                    } catch (Exception e) {
                        LOG.error("Error while updating inventory for sales performa invoice ::::: ", e);
                        throw new SalesPerformaInvoiceException(e.getMessage(), e.getCause());
                    }
                }
            }

            return salesPerformaInvoice;

        }
        throw new SalesPerformaInvoiceException(new SCMError("Dispatch Failed", "Not a valid dispatch event", 701));
    }

    private void updateCurrentPriceForReturns(Map<Integer, CostDetailData> currentPricesMap, SalesPerformaInvoice salesPerformaInvoice) throws SumoException {
        SalesPerformaDetailData salesPerformaDetailData = invoiceDao.find(SalesPerformaDetailData.class, salesPerformaInvoice.getId());
        for (SalesPerformaInvoiceItemData itemData : salesPerformaDetailData.getInvoiceItems()) {
            CostDetailData costDetailData = currentPricesMap.get(itemData.getSkuId());
            if (Objects.isNull(costDetailData)) {
                throw new SumoException("Error Finding Current prices", "Error Occurred While Finding Current prices Of Sku Id : " + itemData.getSkuId() + " : " +
                        scmCache.getSkuDefinition(itemData.getSkuId()).getSkuName());
            }
            itemData.setCurrentPrice(costDetailData.getPrice());
            BigDecimal currentAmount = SCMUtil.multiply(costDetailData.getPrice(), itemData.getQuantity());
            itemData.setCurrentAmount(currentAmount);
            invoiceDao.update(itemData, true);
        }
    }

    private void setDrillDowns(SalesPerformaInvoice salesPerformaInvoice, Map<Integer, CostDetailData> currentPricesMap) throws SumoException {
        List<Integer> keyIds = salesPerformaInvoice.getItems().stream().mapToInt(e -> e.getSku().getId()).boxed().collect(Collectors.toList());
        List<CostDetailData> currentPrices = pricingDao.getCurrentPrices(PriceUpdateEntryType.SKU, salesPerformaInvoice.getUnitId(), keyIds, true);
        if (currentPrices.isEmpty()) {
            throw new SumoException("Error Finding Current prices", "Error Occurred While Finding Current prices ..!");
        } else {
            currentPricesMap.putAll(currentPrices.stream()
                    .collect(Collectors.toMap(CostDetailData::getKeyId, Function.identity(), (a1, a2) -> a1)));
        }
        for (SalesPerformaInvoiceItem invoiceItem : salesPerformaInvoice.getItems()) {
            InventoryItemDrilldown drilldown = new InventoryItemDrilldown();
            drilldown.setKeyId(invoiceItem.getKeyId());
            drilldown.setKeyType(invoiceItem.getKeyType().value());
            drilldown.setQuantity(invoiceItem.getQuantity());
            CostDetailData costDetailData = currentPricesMap.get(invoiceItem.getKeyId());
            if (Objects.isNull(costDetailData)) {
                throw new SumoException("Error Finding Current prices", "Error Occurred While Finding Current prices Of Sku Id : " + invoiceItem.getKeyId() + " : " +
                        scmCache.getSkuDefinition(invoiceItem.getKeyId()).getSkuName());
            }
            drilldown.setPrice(costDetailData.getPrice());
            drilldown.setExpiryDate(costDetailData.getExpiryDate());
            invoiceItem.setDrillDowns(Collections.singletonList(drilldown));
        }
    }

    private void updateAssetPrices(SalesPerformaInvoice salesPerformaInvoice) throws SumoException {
        try {
            SalesPerformaDetailData detailData = invoiceDao.find(SalesPerformaDetailData.class, salesPerformaInvoice.getId());
            // TAKING AVERAGE PRICE OF ASSETS IF THERE ARE MORE THAN 1 ASSETS IN A SALES PERFORMA INVOICE ITEM.
            for (SalesPerformaInvoiceItemData itemData : detailData.getInvoiceItems()) {
                List<EntityAssetMapping> entityAssetMappings = scmAssetManagementService.getAssociatedEntityAssetMapping(detailData.getInvoiceId(),
                        itemData.getItemId(), EntityAssetMappingType.INVOICE.value());
                int counter = 0;
                BigDecimal averagePrice = BigDecimal.ZERO;
                for (EntityAssetMapping entityAssetMapping : entityAssetMappings) {
                    counter++;
                    AssetDefinitionData assetDefinitionData = invoiceDao.find(AssetDefinitionData.class, entityAssetMapping.getAssetId());
                    averagePrice = SCMUtil.divideWithScale10(AssetHelper.getCurrentValueOfAsset(assetDefinitionData), BigDecimal.valueOf(counter));
                }
                itemData.setCurrentPrice(averagePrice);
                BigDecimal currentAmount = SCMUtil.multiply(averagePrice, itemData.getQuantity());
                itemData.setCurrentAmount(currentAmount);
                invoiceDao.update(itemData, true);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While Updating the Asset Prices During Invoicing :: ", e);
            throw new SumoException("PRICING ERROR OF ASSETS", "Error Occurred While Updating the Asset Prices During Invoicing");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SalesPerformaInvoice getInvoice(Integer invoiceId) {
        SalesPerformaDetailData detailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        return SCMDataConverter.convert(detailData, masterDataCache, scmCache);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer updateGeneratedId() {
        Integer count = 0;
        List<SalesPerformaDetailData> invoices = invoiceDao.findAll(SalesPerformaDetailData.class);
        for (SalesPerformaDetailData invoice : invoices) {
            invoice.setGeneratedId(generateInvoiceId(invoice.getVendor(), SalesPerformaType.valueOf(invoice.getType())));
            invoiceDao.update(invoice, false);
            count++;
        }
        invoiceDao.flush();
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SalesPerformaInvoice getCorrectedInvoice(Integer invoiceId) {
        SalesPerformaDetailData detailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        SalesPerformaInvoice performa = SCMDataConverter.convert(detailData, masterDataCache, scmCache);
        if(SalesPerformaType.B2B_RETURN.name().equals(detailData.getType()) || SalesPerformaType.RETURN_TO_VENDOR.name().equals(detailData.getType())) {
            return performa;
        }

        List<SalesPerformaInvoiceCorrected> revisedDetails = invoiceDao.getCorrectedInvoiceDetails(invoiceId,null);
        if(Objects.nonNull(revisedDetails)){
            for(SalesPerformaInvoiceCorrected revisedDetail : revisedDetails){
                CorrectedSalesInvoiceDetails noteDetails =  getCorrectedInvoiceDetails(revisedDetails,revisedDetail.getType(), detailData.getSendingUnit());
                if(revisedDetail.getType().equals(SalesPerformaCorrectedType.CREDIT_NOTE.value())){
                    performa.setCorrectionCreditNoteDetail(noteDetails);
                } else if(revisedDetail.getType().equals(SalesPerformaCorrectedType.DEBIT_NOTE.value())){
                    performa.setCorrectionDebitNoteDetail(noteDetails);
                }
            }
        }
        return performa;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, String> generateB2BInvoice(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException {
        SalesPerformaDetailData detailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        SalesPerformaInvoice invoice = SCMDataConverter.convert(detailData, masterDataCache, scmCache);
        try {
            return uploadB2BInvoiceToS3(invoice, userId);
        } catch (TemplateRenderingException | IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Pair<URL,GoodsReceived> generateSpecializedOrderInvoice(List<GoodsReceived> goodsReceivedList, GoodsReceived aggregatedGr, Integer userId,
                                                                   Integer vendorId) throws SalesPerformaInvoiceException, SumoException {
        try {
            return uploadSpecializedInvoiceToS3(goodsReceivedList, aggregatedGr, userId, vendorId);
        } catch (TemplateRenderingException | IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean saveGrPrDeviations(GoodsReceived goodsReceived ,Integer prId) throws SumoException {
      List<InvoiceExcessQuantity> invoiceExcessQuantityList = new ArrayList<>();
      for(GoodsReceivedItem goodsReceivedItem : goodsReceived.getGoodsReceivedItems()){
          InvoiceExcessQuantity invoiceExcessQuantity = new InvoiceExcessQuantity();
          invoiceExcessQuantity.setInvoiceId(goodsReceived.getInvoiceId());
          invoiceExcessQuantity.setSkuId(goodsReceivedItem.getSkuId());
          invoiceExcessQuantity.setSkuName(goodsReceivedItem.getSkuName());
          invoiceExcessQuantity.setUnitPrice(BigDecimal.valueOf(goodsReceivedItem.getUnitPrice()));
          invoiceExcessQuantity.setUnitId(goodsReceived.getGeneratedForUnitId().getId());
          invoiceExcessQuantity.setExcessQty(BigDecimal.valueOf(goodsReceivedItem.getReceivedQuantity()));
          invoiceExcessQuantityList.add(invoiceExcessQuantity);
      }
      invoiceDao.addAll(invoiceExcessQuantityList);
      return  true;
    }


    /***************************************** private helper methods *************************************************/

    private DocumentDetailData saveToDocumentDetail(FileDetail s3File, Integer userId, String fileName, InvoiceDocType type, FileType fileType, Integer invoiceId) throws SumoException {
        DocumentDetailData documentDetailData = new DocumentDetailData();
        documentDetailData.setUpdatedBy(userId);
        documentDetailData.setDocumentLink(fileName);
        documentDetailData.setFileUrl(s3File.getUrl());
        documentDetailData.setDocumentUploadType(type.name());
        documentDetailData.setDocumentUploadTypeId(invoiceId);
        documentDetailData.setFileType(fileType.name());
        documentDetailData.setMimeType(MimeType.PDF.name());
        documentDetailData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        documentDetailData.setS3Key(s3File.getKey());
        documentDetailData.setS3Bucket(s3File.getBucket());
        documentDetailData = invoiceDao.add(documentDetailData, true);
        return documentDetailData;
    }

    private SalesPerformaInvoice updatePrices(SalesPerformaInvoice finalOutput, SalesPerformaDetailData detailData) throws SumoException, SalesPerformaInvoiceException {
        Map<Integer, SalesPerformaInvoiceItem> itemMap = new HashMap<>();
        for (SalesPerformaInvoiceItem item : finalOutput.getItems()) {
            itemMap.put(item.getId(), item);
        }
        List<SalesPerformaInvoiceItemData> itemDataList = new ArrayList<>();
        for (SalesPerformaInvoiceItemData itemData : detailData.getInvoiceItems()) {
            SalesPerformaInvoiceItem itemFromMap = itemMap.get(itemData.getItemId());
            if (itemFromMap != null) {
                if (Objects.isNull(itemFromMap.getPrice())) {
                    throw new SumoException("Error Finding Current prices", "Error Occurred While Finding Current prices Of Sku Id : " + itemData.getSkuId() + " : " +
                            scmCache.getSkuDefinition(itemData.getSkuId()).getSkuName());
                }
                itemData.setCurrentPrice(itemFromMap.getPrice());
                BigDecimal currentAmount = SCMUtil.multiply(itemFromMap.getPrice(), itemData.getQuantity());
                itemData.setCurrentAmount(currentAmount);
                itemData = invoiceDao.update(itemData, false);
                itemDataList.add(updateDrilldowns(itemData, itemFromMap));
            } else {
                throw new SalesPerformaInvoiceException("Could not find item for updating price after reduce consumable for item id ::: " + itemData.getItemId());
            }
        }
        detailData.setInvoiceItems(itemDataList);
        invoiceDao.flush();
        return SCMDataConverter.convert(detailData, masterDataCache, scmCache);
    }


    private SalesPerformaInvoiceItemData updateDrilldowns(SalesPerformaInvoiceItemData itemData, SalesPerformaInvoiceItem itemFromMap) throws SumoException {
        List<SalesPerformaItemDrilldown> itemDrilldowns = new ArrayList<>();
        for (InventoryItemDrilldown drilldown : itemFromMap.getDrillDowns()) {
            SalesPerformaItemDrilldown itemDrilldown = new SalesPerformaItemDrilldown();
            itemDrilldown.setInvoiceItem(itemData);
            itemDrilldown.setQuantity(drilldown.getQuantity());
            itemDrilldown.setPrice(drilldown.getPrice());
            itemDrilldown.setAddTime(AppUtils.getCurrentTimestamp());
            itemDrilldown.setExpiryDate(drilldown.getExpiryDate());
            invoiceDao.add(itemDrilldown, false);
            itemDrilldowns.add(itemDrilldown);
        }
        itemData.setExpiryDrilldownList(itemDrilldowns);
        return itemData;
    }

    private SalesPerformaInvoice validateAndUpdateInvoice(SalesPerformaInvoice invoice) throws SalesPerformaInvoiceException {
        BigDecimal totalTax = BigDecimal.ZERO;
        BigDecimal totalSellingCost = BigDecimal.ZERO;
        BigDecimal totalCost = BigDecimal.ZERO;
        List<SalesPerformaInvoiceItem> items = new ArrayList<>();
        for (SalesPerformaInvoiceItem item : invoice.getItems()) {
            BigDecimal totalItemTax = item.getTaxes().stream().map(SalesPerformaItemTax::getValue).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal pkgAmount = SCMUtil.multiplyWithScale(item.getPkgPrice(), item.getPkgQty(), 6);
            BigDecimal sellAmount = SCMUtil.multiplyWithScale(item.getSellPrice(), item.getPkgQty(), 6);

            totalTax = SCMUtil.add(totalTax, totalItemTax);
            totalCost = SCMUtil.add(totalCost, pkgAmount);
            totalSellingCost = SCMUtil.add(totalSellingCost, sellAmount);

            item.setTotalTax(totalItemTax);
            item.setPkgAmount(pkgAmount);
            item.setSellAmount(sellAmount);
            items.add(item);
        }
        invoice.getItems().clear();
        invoice.getItems().addAll(items);
        invoice.setTotalTax(totalTax);
        invoice.setTotalCost(totalCost);
        invoice.setTotalAmount(SCMUtil.add(totalSellingCost, totalTax));
        invoice.setTotalSellingCost(totalSellingCost);

        return invoice;
    }


    private SalesPerformaDetailData createInvoiceDetail(SalesPerformaInvoice invoice) throws SumoException {
        SalesPerformaDetailData detailData = new SalesPerformaDetailData();
        detailData.setAssetOrder(SCMUtil.setStatus(invoice.isAssetOrder()));
        detailData.setVendor(invoice.getVendor().getId());
        detailData.setInvoiceType(invoice.getInvoiceType() != null ? invoice.getInvoiceType().name() : InvoiceDocType.INVOICE.name());
        detailData.setDispatchLocation(invoice.getDispatchLocation().getId());
        detailData.setType(invoice.getType().name());
        if (invoice.getPurchaseOrderDate() != null) {
            detailData.setPurchasedOrderDate(invoice.getPurchaseOrderDate());
        }
        if (invoice.getPurchasedOrderNumber() != null) {
            detailData.setPurchasedOrderNumber(invoice.getPurchasedOrderNumber());
        }
        Vehicle vehicle = invoice.getVehicle();
        if (vehicle != null) {
            detailData.setTransportMode(vehicle.getTransportMode());
            detailData.setTransportId(vehicle.getRegistrationNumber());
            detailData.setTransportName(vehicle.getName());
        }
        if (invoice.getBillingType() != null) {
            detailData.setBillingType(invoice.getBillingType());
        }
        detailData.setStatus(SalesPerformaStatus.INITIATED.name());
        detailData.setPoDocumentId(invoice.getDocPOId());
        detailData.setTotalCost(invoice.getTotalCost());
        detailData.setTotalSellingCost(invoice.getTotalSellingCost());
        detailData.setTotalTax(invoice.getTotalTax());
        detailData.setTotalAmount(invoice.getTotalAmount());
        detailData.setAdditionalCharges(invoice.getAdditionalCharges());
        detailData.setSendingUnit(invoice.getSendingUnit().getId());
        detailData.setSendingUnitName(invoice.getSendingUnit().getName());

        detailData.setNeedsApproval(invoice.isNeedsApproval() ? AppConstants.YES : AppConstants.NO);
        detailData.setComment(invoice.getComment());
        detailData.setCreatedAt(SCMUtil.getCurrentTimestamp());
        detailData.setCreatedBy(invoice.getCreatedBy().getId());
        detailData.setDispatchDate(SCMUtil.getDate(invoice.getDispatchDate()));
        detailData.setDocketNumber(invoice.getDocketNumber());
            detailData.setGeneratedId(generateInvoiceId(invoice.getVendor().getId(), invoice.getType()));
        Unit sendingUnit = masterDataCache.getUnit(invoice.getSendingUnit().getId());
        Company sendingCompany = sendingUnit.getCompany();
        detailData.setSendingCompany(sendingCompany.getId());
        detailData.setBusinessType(invoice.getBusinessType());
        if (Objects.nonNull(invoice.getBillingLocationId())) {
            detailData.setBillingLocation(invoice.getBillingLocationId());
        }
        detailData.setBusinessType(invoice.getBusinessType());
        detailData.setReferenceInvoiceNumber(invoice.getReferenceInvoiceNumber());
        detailData.setGstInvoiceType(invoice.getGstInvoiceType());
        detailData = invoiceDao.add(detailData, false);
        return detailData;
    }

    private String generateTransportDocId(SalesPerformaInvoice invoice, Unit transferUnit, VendorDetail toVendor) {
        int InvoiceId = invoice.getId();
        String prefix = AppConstants.COMPANY_SHORT_NAME;
        Collection<IdCodeName> states = masterDataCache.getAllStates(AppConstants.COUNTRY_ID_INDIA);
        String sellerShortCode = "";
        int buyerStateCode = Integer.parseInt(invoice.getTo().getCode());
        for (IdCodeName name : states) {
            if (name.getId() == transferUnit.getLocation().getState().getId()) {
                sellerShortCode = name.getShortCode();
                break;
            }
        }
        return prefix + "/" + InvoiceId + "/" + sellerShortCode + "/" + buyerStateCode;
    }

    @Override
    public String generateDebitNoteDocId(Integer debitNoteId) {
        Integer financialYear = AppUtils.getFinancialYear();
        String prefix = AppConstants.COMPANY_SHORT_NAME;
        if (financialYear <= 2024) {
            return "DN" + "/" + prefix + "/" + "2324" + "/" + debitNoteId;
        } else {
            return "DN" + "/" + prefix + "/" + (financialYear % 100) + "/" + debitNoteId;
        }
    }

    @Override
    public String generateCreditNoteDocId(Integer creditNoteId) {
        Integer financialYear = AppUtils.getFinancialYear();
        String prefix = AppConstants.COMPANY_SHORT_NAME;
        if (financialYear <= 2024) {
            return "CN" + "/" + prefix + "/" + "2324" + "/" + creditNoteId;
        } else {
            return "CN" + "/" + prefix + "/" + (financialYear % 100) + "/" + creditNoteId;
        }
    }

    private String generateInvoiceId(Integer vendorId, SalesPerformaType type) {
        return generateInvoiceIdByFinancialYear(vendorId, type);
    }

    private String generateInvoiceIdByFinancialYear(Integer vendorId, SalesPerformaType type) {
        Integer financialYear = AppUtils.getFinancialYear();
        if (financialYear <= 2024) {
            int vendorInvoiceId = invoiceDao.getNextInvoiceId(vendorId, type.name());
            String prefix = type.equals(SalesPerformaType.B2B_SALES) ? "INV" : "CLN";
            return prefix + "/" + vendorId + "/" + vendorInvoiceId;
        } else {
            int vendorInvoiceId = invoiceDao.getNextInvoiceId(vendorId, type.name(), financialYear);
            String prefix = type.equals(SalesPerformaType.B2B_SALES) ? "INV" : "CLN";
            return prefix + "/" + vendorId + "/" + (financialYear%100)+ "/" +vendorInvoiceId;
        }
    }

    private List<SalesPerformaInvoiceItemData> createInvoiceItemsAndTaxes(List<SalesPerformaInvoiceItem> items, SalesPerformaDetailData detailData) throws SumoException {
        List<SalesPerformaInvoiceItemData> itemList = new ArrayList<>();
        for (SalesPerformaInvoiceItem item : items) {
            SalesPerformaInvoiceItemData itemDetail = new SalesPerformaInvoiceItemData();
            itemDetail.setSkuId(item.getSku().getId());
            itemDetail.setSkuName(item.getSku().getName());
            itemDetail.setTaxCode(item.getSku().getCode());
            itemDetail.setUnitOfMeasure(item.getUom());
            itemDetail.setPackagingId(item.getPkg().getId());
            itemDetail.setPackagingName(item.getPkg().getName());
            itemDetail.setConversionRatio(item.getRatio());
            itemDetail.setPackagingQuantity(item.getPkgQty());
            itemDetail.setQuantity(SCMUtil.multiplyWithScale(item.getPkgQty(), item.getRatio(),6));
            itemDetail.setMappedPrice(item.getPkgPrice());
            itemDetail.setSellingPrice(item.getSellPrice());
            itemDetail.setSellingAmount(item.getSellAmount());
            itemDetail.setMappedAmount(item.getPkgAmount());
            itemDetail.setTotalTax(item.getTotalTax());
            itemDetail.setInvoice(detailData);
            itemDetail.setAlias(item.getAlias());
            itemDetail = invoiceDao.add(itemDetail, false);
            itemDetail.setTaxDetailList(createInvoiceItemTaxes(item.getTaxes(), itemDetail));
            itemList.add(itemDetail);
        }
        return itemList;
    }

    private List<SalesPerformaItemTaxDetail> createInvoiceItemTaxes(List<SalesPerformaItemTax> taxes, SalesPerformaInvoiceItemData item) throws SumoException {
        List<SalesPerformaItemTaxDetail> taxDetailList = new ArrayList<>();
        String hsn = item.getTaxCode();
        for (SalesPerformaItemTax tax : taxes) {
            SalesPerformaItemTaxDetail taxDetail = new SalesPerformaItemTaxDetail();
            taxDetail.setTaxCode(hsn);
            taxDetail.setTaxType(tax.getType().name());
            taxDetail.setPercentage(tax.getPercent());
            taxDetail.setValue(tax.getValue());
            taxDetail.setInvoiceItem(item);
            taxDetail = invoiceDao.add(taxDetail, false);
            taxDetailList.add(taxDetail);
        }
        return taxDetailList;
    }

    private SalesPerformaStatusEventData createInvoiceStatusEvent(SalesPerformaDetailData detailData, Integer empId, SalesPerformaStatus fromStatus, SalesPerformaStatus toStatus) throws SalesPerformaInvoiceException, SumoException {

        if (validateStatus(fromStatus, toStatus)) {
            return createEvent(fromStatus, toStatus, TransitionStatus.SUCCESS, empId, detailData);
        } else {
            LOG.error("Not a valid status update from :: {} :: to :: {} on the transaction :: {}", fromStatus, toStatus, detailData.getInvoiceId());
            createEvent(fromStatus, toStatus, TransitionStatus.FAILURE, empId, detailData);
            throw new SalesPerformaInvoiceException("Not a valid status update for the transaction");
        }
    }

    private boolean validateStatus(SalesPerformaStatus fromStatus, SalesPerformaStatus toStatus) {
        SCMTransitionData data = new SCMTransitionData();
        data.setFromStateCode(fromStatus.name());
        data.setToStateCode(toStatus.name());
        SCMStateTransitionCache.getInstance().setTransitionState(SCMStateTransitionObject.PERFORMA_INVOICE, data);
        return SCMTransitionStatus.SUCCESS.equals(data.getStatus());
    }


    private SalesPerformaStatusEventData createEvent(SalesPerformaStatus fromStatus, SalesPerformaStatus toStatus, TransitionStatus transitionStatus, Integer empId, SalesPerformaDetailData invoice) throws SumoException {
        SalesPerformaStatusEventData event = new SalesPerformaStatusEventData();
        event.setFromStatus(fromStatus.name());
        event.setToStatus(toStatus.name());
        event.setEventStatus(transitionStatus.name());
        event.setGeneratedBy(empId);
        event.setGeneratedAt(SCMUtil.getCurrentTimestamp());
        event.setInvoiceId(invoice.getInvoiceId());
        return invoiceDao.add(event, false);
    }

    private boolean checkForAllUploadsAndSendNotification(SalesPerformaDetailData performa, Integer userId) throws SalesPerformaInvoiceException, SumoException {
        boolean flag = false;
        InvoiceDocType invoiceType = InvoiceDocType.valueOf(performa.getInvoiceType());
        boolean check;
        if (invoiceType.equals(InvoiceDocType.DELIVERY_CHALLAN)) {
            check = performa.geteWayDocUrl() != null;
        } else {
            // validation to check if the invoice is ready for dispatch
            if (performa.getTotalAmount().compareTo(BigDecimal.valueOf(50000)) < 0) {
                check = performa.getInvoiceDocUrl() != null;
            } else {
                check = performa.getInvoiceDocUrl() != null && performa.geteWayDocUrl() != null;
            }
        }

        if (check) {
            flag = sendDispatchNotification(performa, userId);
        }
        return flag;
    }

    private boolean sendDispatchNotification(SalesPerformaDetailData performa, Integer userId) throws SalesPerformaInvoiceException, SumoException {
        if (performa.getStatus().equals(SalesPerformaStatus.PERFORMA_GENERATED.name())) {
            createInvoiceStatusEvent(performa, userId, SalesPerformaStatus.valueOf(performa.getStatus()), SalesPerformaStatus.PENDING_DISPATCH);
            performa.setStatus(SalesPerformaStatus.PENDING_DISPATCH.name());
            invoiceDao.update(performa, true);
        }
        notificationService.sendInvoiceNotification(performa, userId);
        return true;
    }

    private boolean isEwayBillRequired(SalesPerformaDetailData detailData) {
        return SCMUtil.add(detailData.getTotalAmount(), detailData.getAdditionalCharges()).setScale(6, BigDecimal.ROUND_HALF_UP).compareTo(BigDecimal.valueOf(50000).setScale(6, BigDecimal.ROUND_HALF_UP)) >= 0;
    }

    private Map<String, String> uploadB2BInvoiceToS3(SalesPerformaInvoice invoice, Integer userId) throws SumoException, TemplateRenderingException, IOException {
        String auditReportHTMLTemplate = generateB2BInvoiceTemplate(invoice);


        String folder = props.getBasePath() + "/b2binvoice/";
        File b2bFolder = new File(folder);
        if (!b2bFolder.exists()) {
            b2bFolder.mkdirs();
        }
        String path = props.getBasePath() + "/b2binvoice/" + invoice.getId() + ".pdf";
//        String receiptPath = kioskPath + "/" + filepath;
//        File pdfFile = new File(receiptPath);
//        if (!pdfFile.exists()) {
//            pdfFile.createNewFile();
//        }
        try (OutputStream outputStream = new FileOutputStream(path)) {
            HtmlConverter.convertToPdf(auditReportHTMLTemplate, outputStream);
            outputStream.flush();
            File file = new File(props.getBasePath() + "/b2binvoice/" + invoice.getId() + ".pdf");
            try {
                MimeType mimeType = MimeType.PDF;
                return uploadInvoice(mimeType, invoice.getId(), file, userId);
            } catch (Exception e) {
                LOG.error("Encountered error while uploading B2B invoice to S3", e);
                throw new SumoException("Encountered error while uploading B2B invoice to S3");
            }
        } catch (IOException e) {
            LOG.error("Error when trying to create B2B invoice :::::: {}", invoice.getId(), e);
            throw new SumoException("Error creating B2B invoice");
        }
    }

    private GoodsReceived updateTotalAmountForInvoiceRegeneration(GoodsReceived goodsReceived) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (GoodsReceivedItem goodsReceivedItem : goodsReceived.getGoodsReceivedItems()) {
            BigDecimal amountWithoutTax = SCMUtil.multiplyWithScale10(BigDecimal.valueOf(goodsReceivedItem.getUnitPrice()),
                    BigDecimal.valueOf(goodsReceivedItem.getReceivedQuantity()));
            BigDecimal taxAmount = BigDecimal.ZERO;
            if (Objects.nonNull(goodsReceivedItem.getTaxes()) && goodsReceivedItem.getTaxes().size() > 0) {
                for (TaxDetail taxDetail : goodsReceivedItem.getTaxes()) {
                    taxDetail.setTotal(amountWithoutTax);
                    taxDetail.setTaxable(amountWithoutTax);
                    BigDecimal taxValue = SCMUtil.divideWithScale10(SCMUtil.multiplyWithScale10(amountWithoutTax, taxDetail.getPercentage()), BigDecimal.valueOf(100));
                    ;
                    taxDetail.setValue(taxValue);
                    taxAmount = taxAmount.add(taxValue);
                }
            }
            goodsReceivedItem.setCalculatedAmount(SCMUtil.add(amountWithoutTax, taxAmount).floatValue());
            goodsReceivedItem.setTaxAmount(taxAmount.floatValue());
            totalAmount = SCMUtil.add(totalAmount, SCMUtil.add(amountWithoutTax, taxAmount));
        }
        goodsReceived.setTotalAmount(totalAmount.floatValue());

        return goodsReceived;

    }


    private  Pair<URL,GoodsReceived> uploadSpecializedInvoiceToS3(List<GoodsReceived> goodsReceivedList, GoodsReceived aggregatedGr,
                                                                  Integer userId, Integer vendorId ) throws SumoException, TemplateRenderingException, IOException {
        GoodsReceived gr = null;
        Boolean isRegenerated = false;
        String invoiceId = null;
        if (Objects.isNull(aggregatedGr)) {
            gr = goodsReceiveManagementService.getAggregatedObject(goodsReceivedList);
            invoiceId = getInvoiceId(vendorId);
        } else {
            isRegenerated = true;
            gr = aggregatedGr;
            gr = updateTotalAmountForInvoiceRegeneration(gr);
            SpecializedOrderInvoiceData specializedOrderInvoiceData = invoiceDao.find(SpecializedOrderInvoiceData.class,gr.getInvoiceId());
            invoiceId = specializedOrderInvoiceData.getInvoiceId();
        }



        String auditReportHTMLTemplate = generateSpecializedInvoiceTemplate(gr, vendorId,invoiceId);
        String folder = props.getBasePath() + "/specializedOrderInvoice/";
        File b2bFolder = new File(folder);
        if (!b2bFolder.exists()) {
            b2bFolder.mkdirs();
        }
        String path = props.getBasePath() + "/specializedOrderInvoice/" + gr.getId() + ".pdf";
        SpecializedOrderInvoiceData specializedOrderInvoiceData = new SpecializedOrderInvoiceData();
        if(Objects.nonNull(isRegenerated) && Boolean.TRUE.equals(isRegenerated)){
            specializedOrderInvoiceData =  invoiceDao.find(SpecializedOrderInvoiceData.class,aggregatedGr.getInvoiceId());
        }else{
            RequestOrderData requestOrderData = invoiceDao.find(RequestOrderData.class, gr.getRequestOrderId());
            specializedOrderInvoiceData.setUnitId(gr.getGeneratedForUnitId().getId());
            specializedOrderInvoiceData.setVendorId(requestOrderData.getVendorId());
            specializedOrderInvoiceData.setIsPrRaised(AppConstants.NO);
            specializedOrderInvoiceData.setGenerationTime(AppUtils.getCurrentTimestamp());
            specializedOrderInvoiceData.setInvoiceId(invoiceId);
            invoiceDao.add(specializedOrderInvoiceData, true);
        }
        if(Objects.isNull(isRegenerated) || Boolean.FALSE.equals(isRegenerated)){
            for (GoodsReceived goodsReceived : goodsReceivedList) {
                GoodsReceivedData goodsReceivedData = invoiceDao.find(GoodsReceivedData.class, goodsReceived.getId());
                if(Objects.nonNull(goodsReceivedData.getInvoiceId())){
                    throw new SumoException("Invoice Is Already Generated For This Grs !!" , "Please Refresh Once");
                }
                goodsReceivedData.setInvoiceId(specializedOrderInvoiceData.getSpecializedOrderInvoiceId());
                invoiceDao.update(goodsReceivedData, true);
            }
        }

        try (OutputStream outputStream = new FileOutputStream(path)) {
            HtmlConverter.convertToPdf(auditReportHTMLTemplate, outputStream);
            outputStream.flush();
            File file = new File(props.getBasePath() + "/specializedOrderInvoice/" + gr.getId() + ".pdf");
            try {
                MimeType mimeType = MimeType.PDF;
                URL url = new URL(uploadSpecializedOrderInvoice(mimeType, specializedOrderInvoiceData, file, userId).get("invoice"));
                Integer finalAmount = Math.round(gr.getTotalAmount());
                gr.setTotalAmount(finalAmount);
                return new Pair<URL,GoodsReceived>(url,gr);
            } catch (Exception e) {
                LOG.error("Encountered error while uploading B2B invoice to S3", e);
                throw new SumoException("Encountered error while uploading B2B invoice to S3");
            }
        } catch (IOException e) {
            LOG.error("Error when trying to create B2B invoice :::::: {}", gr.getId(), e);
            throw new SumoException("Error creating B2B invoice");
        }
    }

    private String getInvoiceId(Integer vendorId) {
        if (AppUtils.getFinancialYear() <= 2024) {
            return "STPL/" + SCMUtil.getCurrentYear()  + "/MILK/" + invoiceDao.getNextInvoiceId(vendorId,"MILK_BAKERY");
        } else {
            return  "STPL/" + SCMUtil.getCurrentYear()  + "/MILK/" + invoiceDao.getNextInvoiceId(vendorId,"MILK_BAKERY", AppUtils.getFinancialYear());
        }
    }


    private String generateB2BInvoiceTemplate(SalesPerformaInvoice invoice) throws SumoException, TemplateRenderingException, IOException {
        if (invoice != null) {
            DocumentDetailData documentDetailData = new DocumentDetailData();
            if(Objects.nonNull(invoice.getGeneratedBarcodeId())){
                documentDetailData = invoiceDao.find(DocumentDetailData.class, invoice.getGeneratedBarcodeId());
            }
            Map<String, GstSummary> gstSummaryMap = new HashMap<>();
            for (SalesPerformaInvoiceItem item : invoice.getItems()) {
                BigDecimal itemTaxable = item.getSellAmount();
                boolean itemTaxableAdded=false;
                boolean itemTaxableAddedInTotal=false;
                for (SalesPerformaItemTax tax : item.getTaxes()) {
                    BigDecimal gstPercent = tax.getPercent().setScale(1, RoundingMode.HALF_UP);

                    TaxCategoryType taxCategoryType = tax.getType();
                    BigDecimal totalTax = tax.getValue() != null? tax.getValue() : BigDecimal.ZERO;
                    BigDecimal cgst = taxCategoryType.equals(TaxCategoryType.CGST) ? totalTax : BigDecimal.ZERO;
                    BigDecimal sgst = taxCategoryType.equals(TaxCategoryType.SGST) ? totalTax : BigDecimal.ZERO;
                    BigDecimal igst = taxCategoryType.equals(TaxCategoryType.IGST) ? totalTax : BigDecimal.ZERO;

                    GstSummary summary = gstSummaryMap.getOrDefault(gstPercent.toString(), new GstSummary());
                    if(!itemTaxableAdded){
                        itemTaxableAdded=true;
                        summary.setTaxableAmount(summary.getTaxableAmount().add(itemTaxable));
                    }
                    summary.setCgst(summary.getCgst().add(cgst));
                    summary.setSgst(summary.getSgst().add(sgst));
                    summary.setIgst(summary.getIgst().add(igst));

                    GstSummary totalSummary = gstSummaryMap.getOrDefault("Total", new GstSummary());
                    if(!itemTaxableAddedInTotal){
                        itemTaxableAddedInTotal=true;
                        totalSummary.setTaxableAmount(totalSummary.getTaxableAmount().add(itemTaxable));
                    }
                    totalSummary.setCgst(totalSummary.getCgst().add(cgst));
                    totalSummary.setSgst(totalSummary.getSgst().add(sgst));
                    totalSummary.setIgst(totalSummary.getIgst().add(igst));

                    gstSummaryMap.put(gstPercent.toString(), summary);
                    gstSummaryMap.put("Total", totalSummary);
                }
            }
            if(!gstSummaryMap.isEmpty()){
                //sort the GST% keys in ascending order
                Map<String, GstSummary> sortedGstSummaryMap = new LinkedHashMap<>();

                //numeric GST% keys (exclude "Total"), convert to BigDecimal for sorting
                gstSummaryMap.entrySet().stream()
                    .filter(e -> !"Total".equals(e.getKey()))
                    .sorted(Comparator.comparing(e -> new BigDecimal(e.getKey())))
                    .forEach(e -> sortedGstSummaryMap.put(e.getKey(), e.getValue()));

                if (gstSummaryMap.containsKey("Total")) {
                    sortedGstSummaryMap.put("Total", gstSummaryMap.get("Total"));
                }

                gstSummaryMap = sortedGstSummaryMap;
            }
            B2BSalesInvoiceTemplate b2BSalesInvoiceTemplate = new B2BSalesInvoiceTemplate(invoice, props.getBasePath(), masterDataCache.getCompany(invoice.getSendingCompany().getId()), masterDataCache.getUnit(invoice.getUnitId())
                    , NumberToWord.getInstance().convertNumberToWords(invoice.getTotalAmount().add(invoice.getAdditionalCharges()).setScale(0, RoundingMode.UP).intValue()),
                    Objects.nonNull(documentDetailData) ? documentDetailData.getFileUrl():null, gstSummaryMap);
            return b2BSalesInvoiceTemplate.getContent();
        } else {
            throw new SumoException("Please provide valid invoice id");
        }
    }

    private String generateB2BInvoiceReverseTemplate(SalesPerformaInvoice invoice,String creditNote) throws SumoException, TemplateRenderingException, IOException {
        if (invoice != null) {
            DocumentDetailData documentDetailData = invoiceDao.find(DocumentDetailData.class, invoice.getGeneratedBarcodeId());

            B2BReturnSalesInvoiceTemplate b2BSalesInvoiceTemplate = new B2BReturnSalesInvoiceTemplate(invoice, props.getBasePath(), masterDataCache.getCompany(invoice.getSendingCompany().getId()), masterDataCache.getUnit(invoice.getUnitId())
                    , NumberToWord.getInstance().convertNumberToWords(invoice.getTotalAmount().add(invoice.getAdditionalCharges()).setScale(0, RoundingMode.UP).intValue()),
                    documentDetailData.getFileUrl(),
                    AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate()),creditNote);
            return b2BSalesInvoiceTemplate.getContent();
        } else {
            throw new SumoException("Please provide valid invoice id");
        }
    }

    private String generateCreditCorrectionTemplate(SalesPerformaInvoice invoice,CorrectedSalesInvoiceDetails correctedInvoice, String creditNote) throws SumoException, TemplateRenderingException, IOException {
        if (Objects.nonNull(invoice) && Objects.nonNull(correctedInvoice)) {
            DocumentDetailData documentDetailData = invoiceDao.find(DocumentDetailData.class, correctedInvoice.getBarcodeId());
            BigDecimal netAmount = BigDecimal.ZERO;
            BigDecimal totalTax = BigDecimal.ZERO;
            for(CorrectedSalesInvoiceItemDetails item : correctedInvoice.getSalesPerformaCorrectedItems()){
                BigDecimal amt = SCMUtil.multiplyWithScale(item.getCorrectedPkgQty(),item.getCorrectedPrice(),4);
                BigDecimal tax = SCMUtil.subtract(item.getTax(),item.getRevisedTax()).abs();
                netAmount = netAmount.add(amt);
                totalTax = totalTax.add(tax);
            }

            CorrectionCreditTemplate b2BSalesInvoiceTemplate = new CorrectionCreditTemplate(
                    invoice,
                    correctedInvoice,
                    props.getBasePath(),
                    netAmount,
                    totalTax,
                    masterDataCache.getCompany(invoice.getSendingCompany().getId()),
                    masterDataCache.getUnit(invoice.getUnitId()),
                    documentDetailData.getFileUrl(),
                    AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate()),
                    creditNote);
            return b2BSalesInvoiceTemplate.getContent();
        } else {
            throw new SumoException("Please provide valid invoice id");
        }
    }

    private String generateDebitCorrectionTemplate(SalesPerformaInvoice invoice,CorrectedSalesInvoiceDetails correctedInvoice,String debitNote) throws SumoException, TemplateRenderingException, IOException {
        if (Objects.nonNull(invoice) && Objects.nonNull(correctedInvoice)) {
            DocumentDetailData documentDetailData = invoiceDao.find(DocumentDetailData.class, correctedInvoice.getBarcodeId());
            BigDecimal netAmount = BigDecimal.ZERO;
            BigDecimal totalTax = BigDecimal.ZERO;
            for(CorrectedSalesInvoiceItemDetails item : correctedInvoice.getSalesPerformaCorrectedItems()){
                BigDecimal amt = SCMUtil.multiplyWithScale(item.getCorrectedPkgQty(),item.getCorrectedPrice(),4);
                BigDecimal tax = SCMUtil.subtract(item.getTax(),item.getRevisedTax()).abs();
                netAmount = netAmount.add(amt);
                totalTax = totalTax.add(tax);
            }
            CorrectionDebitTemplate b2BSalesInvoiceTemplate = new CorrectionDebitTemplate(invoice,correctedInvoice, props.getBasePath(),
                    netAmount,totalTax,masterDataCache.getCompany(invoice.getSendingCompany().getId()),
                    masterDataCache.getUnit(invoice.getUnitId())
                    ,documentDetailData.getFileUrl(),
                    AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate()),debitNote);
            return b2BSalesInvoiceTemplate.getContent();
        } else {
            throw new SumoException("Please provide valid invoice id");
        }
    }

    private String generateCreditNoteByFinancialYear(String type,Integer stateId) {
        Integer financialYear = AppUtils.getFinancialYear();
        if (financialYear <= 2024) {
            int creditNoteId = transferOrderManagementDao.getNextStateInvoiceId(stateId, type);
            return "CN/STPL/2324/" + creditNoteId;
        } else {
            int creditNoteId = transferOrderManagementDao.getNextStateInvoiceId(stateId, type, financialYear);
            return "CN/STPL/" + (financialYear%100)+ "/" +creditNoteId;
        }
    }

    private String generateDebitNoteByFinancialYear(String type,Integer stateId) {
        Integer financialYear = AppUtils.getFinancialYear();
        if (financialYear <= 2024) {
            int debitNoteId = transferOrderManagementDao.getNextStateInvoiceId(stateId, type);
            return "DN/STPL/2324/" + debitNoteId;
        } else {
            int debitNoteId = transferOrderManagementDao.getNextStateInvoiceId(stateId, type, financialYear);
            return "DN/STPL/" + (financialYear%100)+ "/" + debitNoteId;
        }
    }

    private String generateDebitNoteTemplate(SalesPerformaInvoice invoice, String debitNote) throws SumoException, TemplateRenderingException, IOException {
        if (invoice != null) {
            DocumentDetailData documentDetailData = invoiceDao.find(DocumentDetailData.class, invoice.getGeneratedBarcodeId());
            DebitNoteTemplate debitNoteTemplate = new DebitNoteTemplate(invoice, props.getBasePath(), masterDataCache.getCompany(invoice.getSendingCompany().getId()), masterDataCache.getUnit(invoice.getUnitId())
                    , NumberToWord.getInstance().convertNumberToWords(invoice.getTotalAmount().add(invoice.getAdditionalCharges()).setScale(0, RoundingMode.UP).intValue()),
                    documentDetailData.getFileUrl(),
                    AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate()),debitNote);
            return debitNoteTemplate.getContent();
        } else {
            throw new SumoException("Please provide valid invoice id");
        }
    }

    private String generateSpecializedInvoiceTemplate(GoodsReceived goodsReceived, Integer vendorId , String invoiceId) throws SumoException, TemplateRenderingException, IOException {
        if (Objects.nonNull(goodsReceived)) {
            VendorDetail vendorDetail = scmCache.getVendorDetail(vendorId);
            Float totalTax = Float.valueOf(0);
            Float totalCsgt = Float.valueOf(0);
            Float totalSgst = Float.valueOf(0);
            for (GoodsReceivedItem grItem : goodsReceived.getGoodsReceivedItems()) {
                if (Objects.nonNull(grItem.getTaxAmount())) {
                    totalTax += grItem.getTaxAmount();
                }
                if (Objects.nonNull(grItem.getTaxAmount()) && grItem.getTaxes().size() > 0) {
                    for (TaxDetail taxDetail : grItem.getTaxes()) {
                        switch (taxDetail.getCode()) {
                            case "CGST":
                                totalCsgt += taxDetail.getValue().floatValue();
                                break;
                            case "SGST":
                                totalSgst += taxDetail.getValue().floatValue();
                                break;
                        }
                    }
                }
            }
            Float originalValue = goodsReceived.getTotalAmount();
            Integer finalAmount = Math.round(goodsReceived.getTotalAmount());
            Float roundedOffAmount = Math.abs(SCMUtil.subtract(originalValue, Float.valueOf(finalAmount)));

            Unit unit = masterDataCache.getUnit(goodsReceived.getGeneratedForUnitId().getId());

            VendorDispatchLocation vendorDispatchLocation = vendorDetail.getDispatchLocations().stream().filter(loc ->
                            loc.getState().equals(unit.getAddress().getState())).
                    findFirst().orElseGet(null);
            if(Objects.isNull(vendorDispatchLocation)){
                vendorDispatchLocation = vendorDetail.getDispatchLocations().get(0);
            }

            SpecializedOrderInvoice specializedOrderInvoice = new SpecializedOrderInvoice(goodsReceived, props.getBasePath(), masterDataCache.getCompany(goodsReceived.getSourceCompany().getId()),
                    unit, NumberToWord.getInstance().convertNumberToWords(finalAmount), vendorDetail,
                    totalTax, totalCsgt, totalSgst, roundedOffAmount, finalAmount, invoiceId, vendorDispatchLocation);
            //goodsReceived.setTotalAmount(finalAmount);
            return specializedOrderInvoice.getContent();
        } else {
            throw new SumoException("Please provide valid invoice id ::::");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean saveDownloadJsonDocIdNo(Integer invoiceId, String docId) {
        SalesPerformaDetailData salesPerformaDetailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        if (salesPerformaDetailData != null) {
            salesPerformaDetailData.setUploadDocId(docId);
            invoiceDao.update(salesPerformaDetailData, true);
            return true;
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean uploadInvoiceSheet(MultipartFile file, Integer invoiceId, String type) throws IOException, SumoException {
        InputStream excelFile = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(excelFile);
        Sheet dataTypeSheet = workbook.getSheetAt(0);
        Row headerRow = dataTypeSheet.getRow(0);
        for (int i = 1; i <= dataTypeSheet.getLastRowNum(); i++) {
            Row row = dataTypeSheet.getRow(i);
            String docId = row.getCell(4).getStringCellValue();
            SalesPerformaDetailData salesPerformaDetailData = new SalesPerformaDetailData();
            if(type.equals(SalesPerformaType.RETURN_TO_VENDOR.value())){
                salesPerformaDetailData = invoiceDao.findByGeneratedDebitNoteId(docId);
            }else if(type.equals(SalesPerformaType.B2B_RETURN.value())){
                salesPerformaDetailData = invoiceDao.findByGeneratedCreditNoteId(docId);
            }else {
                salesPerformaDetailData = invoiceDao.findByGeneratedId(docId);
            }
            if (salesPerformaDetailData != null && salesPerformaDetailData.getInvoiceId().equals(invoiceId)) {
                salesPerformaDetailData.setIrnNo((row.getCell(1).getStringCellValue()));
                salesPerformaDetailData.setUploadedAckNo(row.getCell(2).getStringCellValue());
                salesPerformaDetailData.setSignedQrCode(row.getCell(10).toString());
                salesPerformaDetailData.setUploadedEwayNo(row.getCell(11).getStringCellValue());
                try {
                    QRGenerationServiceImpl impl = new QRGenerationServiceImpl();
                    byte[] generatedBarCode = impl.getQRCodeImage(salesPerformaDetailData.getSignedQrCode(), 100, 100);
                    InputStream inputStream = new ByteArrayInputStream(generatedBarCode);
                    MultipartFile barcodeFile = new MockMultipartFile("new file name", "Original file name", ContentType.IMAGE_PNG.toString(), inputStream);
                    String fileName = "BARCODE_" + salesPerformaDetailData.getInvoiceId() + "." + MimeType.PNG.name().toLowerCase();
                    FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_BARCODE/INVOICE", fileName, barcodeFile);
                    DocumentDetail documentDetail = new DocumentDetail();
                    documentDetail.setMimeType(MimeType.PNG);
                    documentDetail.setUploadType(DocUploadType.INVOICE_BARCODE);
                    documentDetail.setFileType(FileType.SALES_INVOICE);
                    documentDetail.setDocumentLink(fileName);
                    documentDetail.setS3Key(s3File.getKey());
                    documentDetail.setFileUrl(s3File.getUrl());
                    documentDetail.setS3Bucket(s3File.getBucket());
                    documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(salesPerformaDetailData.getCreatedBy(), "", masterDataCache.getEmployee(salesPerformaDetailData.getCreatedBy())));
                    documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
                    DocumentDetailData data = invoiceDao.add(SCMDataConverter.convert(documentDetail), true);
                    salesPerformaDetailData.setGeneratedBarcodeId(data.getDocumentId());
                } catch (Exception e) {
                    LOG.error("Encountered error while uploading document", e);
                }
                invoiceDao.update(salesPerformaDetailData, true);
            } else {
                throw new SumoException("Cannot Find Invoice for the Invoice Id");
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SalesPerformaInvoice approveCancellation(Integer invoiceId, Integer userId, Integer docId,String salesPerformaStatus) throws SalesPerformaInvoiceException, SumoException, InventoryUpdateException {
        try {
            LOG.info(" Request for Approval or Disappoval of Cancellation Request ");
            SalesPerformaDetailData detailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
            String status = SalesPerformaStatus.CANCELLED.name();
            if(salesPerformaStatus.equals(SalesPerformaStatus.APPROVED.name())){
                status = SalesPerformaStatus.CANCELLED.name();
            }else if(salesPerformaStatus.equals(SalesPerformaStatus.DISAPPROVED.name())){
                status = SalesPerformaStatus.PENDING_DISPATCH.name();
            }
            createInvoiceStatusEvent(detailData, userId, SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.valueOf(status));
            if(!status.equals(SalesPerformaStatus.CANCELLED.name())) {
                detailData.setCancelledAt(SCMUtil.getCurrentTimestamp());
                detailData.setCancelledBy(userId);
                detailData.setNeedsApproval(AppConstants.NO);
                detailData.setCancelDocId(docId);
            }
            detailData.setStatus(status);
            detailData = invoiceDao.update(detailData, true);
            SalesPerformaInvoice invoice = SCMDataConverter.convert(detailData, masterDataCache, scmCache);
            return invoice;
        }catch (Exception e){
            LOG.error("Error While Approving cancellation for invoiceid :: {}",invoiceId);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void cancelOldInvoices(Date oldDate) throws SalesPerformaInvoiceException, SumoException {
        try {
            LOG.info(" CRON :: Getting Old Invoices for Marking Status Cancelled  ");
            List<String> status = new ArrayList<>();
            status.add(SalesPerformaStatus.APPROVED.name());
            status.add(SalesPerformaStatus.PERFORMA_GENERATED.name());
            status.add(SalesPerformaStatus.PENDING_DISPATCH.name());
            status.add(SalesPerformaStatus.PENDING_CANCEL_APPROVAL.name());
            List<Integer> oldInvoiceRequestIds = invoiceDao.getOldInvoiceIds(oldDate, status);
            if (Objects.nonNull(oldInvoiceRequestIds)) {
                for (Integer invoiceId : oldInvoiceRequestIds) {
                    SalesPerformaDetailData detailData = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
                    createInvoiceStatusEvent(detailData, AppConstants.SYSTEM_EMPLOYEE_ID,SalesPerformaStatus.valueOf(detailData.getStatus()), SalesPerformaStatus.CANCELLED);
                    detailData.setStatus(SalesPerformaStatus.CANCELLED.name());
                    detailData.setCancelledAt(SCMUtil.getCurrentTimestamp());
                    detailData.setCancelledBy(AppConstants.SYSTEM_EMPLOYEE_ID);
                    detailData.setNeedsApproval(AppConstants.NO);
                    detailData = invoiceDao.update(detailData, true);
                    if (Objects.nonNull(detailData)) {
                        LOG.info("Sales Invoice Request Auto Cancelled with Invoice Id :: {}", detailData.getInvoiceId());
                    }
                }
            }
        } catch (Exception e) {
            LOG.error(" Error While Auto cancellation of Invoice id ");
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveCorrectedInvoiceDetails(SalesPerformaInvoice invoice,Integer userId) throws Exception {

        LOG.info(" Saving revised invoice details for invoice ::{} ",invoice.getInvoice());
        SalesPerformaInvoiceCorrected creditNoteDetail = new SalesPerformaInvoiceCorrected();
        SalesPerformaInvoiceCorrected debitNoteDetail = new SalesPerformaInvoiceCorrected();
        List<SalesPerformaInvoiceCorrected> revisedDetails = invoiceDao.getCorrectedInvoiceDetails(invoice.getId(),null);

        if(Objects.nonNull(revisedDetails)){
            for(SalesPerformaInvoiceCorrected revisedDetail : revisedDetails){
                if(revisedDetail.getType().equals(SalesPerformaCorrectedType.CREDIT_NOTE.value())){
                    creditNoteDetail = revisedDetail;
                } else if(revisedDetail.getType().equals(SalesPerformaCorrectedType.DEBIT_NOTE.value())){
                    debitNoteDetail = revisedDetail;
                }
            }
        }

        SalesPerformaDetailData detail = invoiceDao.find(SalesPerformaDetailData.class,invoice.getId());
        SalesPerformaInvoice salesPerformaInvoice = SCMDataConverter.convert(detail, masterDataCache, scmCache);

        createInvoiceStatusEvent(detail, userId, SalesPerformaStatus.valueOf(detail.getStatus()), SalesPerformaStatus.CORRECTION_APPROVAL_L1);
        detail.setStatus(SalesPerformaStatus.CORRECTION_APPROVAL_L1.value());
        invoiceDao.update(detail,false);

        if (Objects.nonNull(debitNoteDetail.getSalesPerformaCorrectedItems())) {
            for (SalesPerformaInvoiceItemCorrected item : debitNoteDetail.getSalesPerformaCorrectedItems()) {
                invoiceDao.delete(item);
            }
            invoiceDao.delete(debitNoteDetail);
            debitNoteDetail = new SalesPerformaInvoiceCorrected();
        }
        if (Objects.nonNull(creditNoteDetail.getSalesPerformaCorrectedItems())) {
            for (SalesPerformaInvoiceItemCorrected item : creditNoteDetail.getSalesPerformaCorrectedItems()) {
                invoiceDao.delete(item);
            }
            invoiceDao.delete(creditNoteDetail);
            creditNoteDetail = new SalesPerformaInvoiceCorrected();

        }

        List<SalesPerformaInvoiceItem> items = invoice.getItems();
        List<SalesPerformaInvoiceItemCorrected> debitNoteItems = new ArrayList<>();
        List<SalesPerformaInvoiceItemCorrected> creditNoteItems = new ArrayList<>();

        for (SalesPerformaInvoiceItem item : items) {
            BigDecimal curAmt = SCMUtil.multiplyWithScale(item.getPkgQty(), item.getSellPrice(),4);
            BigDecimal newAmt = SCMUtil.multiplyWithScale(item.getCorrectedPkgQty(), item.getCorrectedSellPrice(),4);
            SalesPerformaInvoiceItemCorrected debitItem;
            SalesPerformaInvoiceItemCorrected creditItem;
            if (SCMUtil.subtract(newAmt, curAmt).compareTo(BigDecimal.ZERO) > 0) {
                debitItem = SCMDataConverter.convert(item, invoice.getId(), debitNoteDetail);
                debitNoteItems.add(debitItem);
            } else if (SCMUtil.subtract(newAmt, curAmt).compareTo(BigDecimal.ZERO) < 0) {
                creditItem = SCMDataConverter.convert(item, invoice.getId(), creditNoteDetail);
                creditNoteItems.add(creditItem);
            }
        }

        if (!creditNoteItems.isEmpty()) {
            creditNoteDetail.setInvoiceId(invoice.getId());
            creditNoteDetail.setType(SalesPerformaCorrectedType.CREDIT_NOTE.value());
            creditNoteDetail.setInvoiceStatus(SalesPerformaStatus.CORRECTION_APPROVAL_L1.value());
            creditNoteDetail = invoiceDao.add(creditNoteDetail, true);
            List<SalesPerformaInvoiceItemCorrected> creditItemList = new ArrayList<>();
            for (SalesPerformaInvoiceItemCorrected creditNoteItem : creditNoteItems) {
                creditNoteItem.setSalesPerformaInvoiceCorrected(creditNoteDetail);
                creditNoteItem = invoiceDao.add(creditNoteItem, true);
                creditItemList.add(creditNoteItem);
            }
            creditNoteDetail.setSalesPerformaCorrectedItems(creditItemList);
            invoiceDao.update(creditNoteDetail, false);
        }

        if (!debitNoteItems.isEmpty()) {
            debitNoteDetail.setInvoiceId(invoice.getId());
            debitNoteDetail.setType(SalesPerformaCorrectedType.DEBIT_NOTE.value());
            debitNoteDetail.setInvoiceStatus(SalesPerformaStatus.CORRECTION_APPROVAL_L1.value());
            debitNoteDetail = invoiceDao.add(debitNoteDetail, true);
            List<SalesPerformaInvoiceItemCorrected> debitItemList = new ArrayList<>();
            for(SalesPerformaInvoiceItemCorrected debitNoteItem : debitNoteItems){
                debitNoteItem.setSalesPerformaInvoiceCorrected(debitNoteDetail);
                debitNoteItem = invoiceDao.add(debitNoteItem,true);
                debitItemList.add(debitNoteItem);
            }
            debitNoteDetail.setSalesPerformaCorrectedItems(debitItemList);
            invoiceDao.update(debitNoteDetail, false);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CorrectedSalesInvoiceDetails getCorrectedInvoiceDetails(List<SalesPerformaInvoiceCorrected> detailData , String type, Integer unitId)  {
        CorrectedSalesInvoiceDetails details = new CorrectedSalesInvoiceDetails();
        for(SalesPerformaInvoiceCorrected spic : detailData){
            if(spic.getType().equals(type)) {
                details = SCMDataConverter.convert(spic);
            }
        }
        // adding taxCode...
        List<Integer> skuIds = details.getSalesPerformaCorrectedItems().stream().map(CorrectedSalesInvoiceItemDetails::getSkuId).collect(Collectors.toList());
        Map<Integer, String> skuIdTaxCodeMap = getSkuTaxCode(skuIds, unitId);
        for(CorrectedSalesInvoiceItemDetails item : details.getSalesPerformaCorrectedItems()) {
            String taxCode = skuIdTaxCodeMap.get(item.getSkuId());
            if(StringUtils.isNotBlank(taxCode)) {
                item.setTaxCode(taxCode);
                continue;
            }
            SkuDefinition sku = scmCache.getSkuDefinition(item.getSkuId());
            if(sku != null) {
                if(StringUtils.isNotBlank(sku.getTaxCode())) {
                    item.setTaxCode(sku.getTaxCode());
                    continue;
                }
                ProductDefinition pd = scmCache.getProductDefinition(sku.getLinkedProduct().getId());
                if(pd != null && StringUtils.isNotBlank(pd.getTaxCode())) {
                    item.setTaxCode(pd.getTaxCode());
                }
            }
        }
        return details;
    }

    private Map<Integer, String> getSkuTaxCode(List<Integer> skuIds, Integer unitId) {
        Map<Integer, String> skuTaxMap = new HashMap<>();
        for(Object[] a : invoiceDao.getSkuTaxCode(skuIds, unitId)) {
            skuTaxMap.put((Integer) a[0], (String)a[1]);
        }
        return skuTaxMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<CorrectedSalesInvoiceDetails> getCorrectedInvoiceDetails(Integer invoiceId , String type)  {

        LOG.info("Getting Revised Invoice Item details for invoice :{} ",invoiceId);
        List<SalesPerformaInvoiceCorrected> detailData = invoiceDao.getCorrectedInvoiceDetails(invoiceId,type);
        List<CorrectedSalesInvoiceDetails> details = new ArrayList<>();
        for(SalesPerformaInvoiceCorrected spic : detailData){
            details.add(SCMDataConverter.convert(spic));
        }
        return details;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveCreditDebitNoteDetails(CreditDebitNoteDetail creditDebitNoteDetail) throws Exception {

        SalesPerformaInvoiceCreditDebitNoteDetail detail = saveCreditDebitNoteDetail(creditDebitNoteDetail);
        List<SalesPerformaInvoiceCreditDebitNoteItemDetail> itemDataList = saveCreditDebitNoteItemDetails(creditDebitNoteDetail.getItemDetails(),detail);
        detail.setCreditDebitNoteItems(itemDataList);

        invoiceDao.add(detail, true);
        return true;
    }

    private SalesPerformaInvoiceCreditDebitNoteDetail saveCreditDebitNoteDetail(CreditDebitNoteDetail creditDebitNoteDetail) throws SumoException {
        SalesPerformaInvoiceCreditDebitNoteDetail detail = new SalesPerformaInvoiceCreditDebitNoteDetail();
        detail.setInvoiceId(creditDebitNoteDetail.getInvoiceId());
        detail.setVendorId(creditDebitNoteDetail.getVendorId());
        detail.setStatus(SalesPerformaStatus.PENDING_APPROVAL_L1.name());
        detail.setCreatedBy(creditDebitNoteDetail.getCreatedBy());
        detail.setGenerationTime(SCMUtil.getCurrentTimestamp());
        detail.setApprovalRequired(SCMUtil.YES);
        detail.setCreditNoteId(creditDebitNoteDetail.getCreditNoteId());
        detail.setCreditNoteDocId(creditDebitNoteDetail.getCreditNoteDocId());
        detail.setCreditNoteDocUrl(creditDebitNoteDetail.getCreditNoteDocUrl());
        detail.setInvoiceDocUrl(creditDebitNoteDetail.getInvoiceDocUrl());
        detail.setInvoiceDate(SCMUtil.parseDate(creditDebitNoteDetail.getInvoiceDate()));
        detail.setVendorInvoiceNumber(creditDebitNoteDetail.getVendorInvoiceNumber());
        BigDecimal tax = BigDecimal.ZERO;
        BigDecimal netAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        for(CreditDebitNoteItemDetail itemDetail : creditDebitNoteDetail.getItemDetails()){
            tax = SCMUtil.add(tax,itemDetail.getTaxAmount());
            netAmount = SCMUtil.add(netAmount,SCMUtil.multiply(itemDetail.getQty(),itemDetail.getPrice()));
            totalAmount = SCMUtil.add(totalAmount,SCMUtil.add(SCMUtil.multiply(itemDetail.getQty(),itemDetail.getPrice()),itemDetail.getTaxAmount()));
        }
        detail.setTotalTax(tax);
        detail.setNetAmount(netAmount);
        detail.setTotalAmount(totalAmount);
        invoiceDao.add(detail, false);
        return detail;
    }

    private List<SalesPerformaInvoiceCreditDebitNoteItemDetail> saveCreditDebitNoteItemDetails(List<CreditDebitNoteItemDetail> creditDebitNoteItemDetails, SalesPerformaInvoiceCreditDebitNoteDetail detail) throws SumoException {
        List<SalesPerformaInvoiceCreditDebitNoteItemDetail> itemList = new ArrayList<>();
        for (CreditDebitNoteItemDetail item : creditDebitNoteItemDetails) {
            SalesPerformaInvoiceCreditDebitNoteItemDetail itemDetail = new SalesPerformaInvoiceCreditDebitNoteItemDetail();
            itemDetail.setItemDesc(item.getItemDesc());
            itemDetail.setQty(item.getQty());
            itemDetail.setPrice(item.getPrice());
            itemDetail.setNetAmount(SCMUtil.multiply(item.getQty(),item.getPrice()));
            itemDetail.setTaxPercent(item.getTaxPercent());
            itemDetail.setTaxAmount(item.getTaxAmount());
            itemDetail.setTaxPercent(SCMUtil.divide(SCMUtil.multiply(item.getTaxAmount(),BigDecimal.valueOf(100)),SCMUtil.multiply(item.getQty(),item.getPrice())));
            itemDetail.setTotalAmount(SCMUtil.add(SCMUtil.multiply(item.getQty(),item.getPrice()),item.getTaxAmount()));
            itemDetail.setCreditDebitNoteDetail(detail);
            invoiceDao.add(itemDetail, false);
            itemList.add(itemDetail);
        }
        return itemList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CreditDebitNoteDetail> getCreditDebitNoteDetails(Date startDate, Date endDate, String status,Integer vendorId) throws Exception {

        List<SalesPerformaInvoiceCreditDebitNoteDetail> creditDebitNoteDetailList = invoiceDao.getCreditDebitNoteDetails(startDate,endDate,status,vendorId);
        List<CreditDebitNoteDetail> detailList = new ArrayList<>();
        if(Objects.isNull(creditDebitNoteDetailList)){
            return  null;
        }
        for(SalesPerformaInvoiceCreditDebitNoteDetail salesDetail : creditDebitNoteDetailList){
            CreditDebitNoteDetail detail = new CreditDebitNoteDetail();
            detail.setId(salesDetail.getId());
            detail.setInvoiceId(salesDetail.getInvoiceId());
            detail.setVendorId(salesDetail.getVendorId());
            detail.setStatus(salesDetail.getStatus());
            detail.setCreatedBy(salesDetail.getCreatedBy());
            detail.setGenerationTime(salesDetail.getGenerationTime());
            detail.setApprovalRequired(salesDetail.getApprovalRequired());
            detail.setApprovedBy(salesDetail.getApprovedBy());
            detail.setUpdatedAt(salesDetail.getUpdatedAt());
            detail.setCreditNoteId(salesDetail.getCreditNoteId());
            detail.setCreditNoteDocUrl(salesDetail.getCreditNoteDocUrl());
            detail.setCreditNoteDocId(salesDetail.getCreditNoteDocId());
            detail.setInvoiceDocUrl(salesDetail.getInvoiceDocUrl());
            detailList.add(detail);
        }

        return  detailList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean approveCreditNote(CreditDebitNoteDetail creditDebitNoteDetail , Integer userId) throws Exception {
        SalesPerformaInvoiceCreditDebitNoteDetail detail = invoiceDao.find(SalesPerformaInvoiceCreditDebitNoteDetail.class,creditDebitNoteDetail.getId());
        if(detail.getStatus().equals(SalesPerformaStatus.PENDING_APPROVAL_L1.name())){
            detail.setStatus(SalesPerformaStatus.PENDING_APPROVAL_L2.name());
            detail.setApprovedBy(userId);
            detail.setApprovalRequired(SCMUtil.YES);
        }else if(detail.getStatus().equals(SalesPerformaStatus.PENDING_APPROVAL_L2.name())){
            Integer invoiceId = getLastInvoiceId(creditDebitNoteDetail.getVendorId());
        SalesPerformaDetailData salesPerformaDetailData = invoiceDao.find(SalesPerformaDetailData.class,invoiceId);
        SalesPerformaInvoice invoice = SCMDataConverter.convert(salesPerformaDetailData, masterDataCache, scmCache);
        DocumentDetailData data = saveCreditNotePriceGapTemplate(detail,creditDebitNoteDetail.getCreatedBy(),creditDebitNoteDetail.getId(),invoice);
        detail.setCreditNoteDocUrl(data.getFileUrl());
        detail.setCreditNoteDocId(data.getDocumentId());
        detail.setStatus(SalesPerformaStatus.CLOSED.name());
        detail.setApprovedBy(userId);
        detail.setApprovalRequired(SCMUtil.NO);
            sendMail(data,invoice,creditDebitNoteDetail);
        }
        detail.setUpdatedAt(SCMUtil.getCurrentTimestamp());
        invoiceDao.update(detail,false);
        return true;
    }

    public void sendMail(DocumentDetailData documentDetailData, SalesPerformaInvoice invoice ,CreditDebitNoteDetail creditDebitNoteDetail) throws FileNotFoundException {
        try{
            CreditNoteGenerationTemplate creditNoteGenerationTemplate = new CreditNoteGenerationTemplate(documentDetailData,invoice,props);
            CreditNoteGenerationNotification notification = new CreditNoteGenerationNotification(props.getEnvType(),creditNoteGenerationTemplate,invoice.getVendor().getName());
            FileDetail fileDetail = new FileDetail();
            fileDetail.setBucket(documentDetailData.getS3Bucket());
            fileDetail.setKey(documentDetailData.getS3Key());
            fileDetail.setUrl(documentDetailData.getFileUrl());
            List<AttachmentData> attachments = new ArrayList<>();
            File creditNoteDetail = fileArchiveService.getFileFromS3(props.getBasePath() + File.separator + "s3",fileDetail);
            AttachmentData creditNote = new AttachmentData(IOUtils.toByteArray(new FileInputStream(creditNoteDetail)), "CREDIT_NOTE",
                    MimeType.mimeValue(documentDetailData.getMimeType()));
            attachments.add(creditNote);
            notification.sendRawMail(attachments);
        }catch (Exception e){
            LOG.error("Error sending mail for credit note approval for credit note : {} ",creditDebitNoteDetail.getId());
            LOG.error(e.getMessage());
        }
    }


    public Integer getLastInvoiceId(Integer vendorId) {
        return invoiceDao.getLastInvoiceId(vendorId);
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean rejectCreditNote(CreditDebitNoteDetail creditDebitNoteDetail, Integer userId) throws Exception {
        SalesPerformaInvoiceCreditDebitNoteDetail detail = invoiceDao.find(SalesPerformaInvoiceCreditDebitNoteDetail.class,creditDebitNoteDetail.getId());
        detail.setStatus(SalesPerformaStatus.REJECTED.name());
        detail.setApprovedBy(userId);
        detail.setUpdatedAt(SCMUtil.getCurrentTimestamp());
        detail.setApprovalRequired(SCMUtil.NO);
        invoiceDao.update(detail,false);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadVendorInvoice(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file) {
        String fileName =  "VENDOR_INVOICE_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "." + mimeType.extension();
        try {
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "vendorInvoices", fileName, file);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = purchaseOrderManagementDao.add(SCMDataConverter.convert(documentDetail), true);
            if (data.getDocumentId() != null) {
                return SCMDataConverter.convert(data);
            }
        } catch (Exception e) {
            LOG.error("Encountered error while uploading document", e);
        }
        return null;
    }

    private DocumentDetailData saveCreditNotePriceGapTemplate(SalesPerformaInvoiceCreditDebitNoteDetail creditDebitNoteDetail,Integer userId,Integer creditNoteNo,SalesPerformaInvoice invoice) throws SumoException, TemplateRenderingException, IOException {

        String fileName = "CREDIT_NOTE_" +  SCMUtil.getCurrentTimeISTStringWithNoColons() + ".pdf";
        String invoiceHTMLTemplate = generateCreditNotePriceGapTemplate(creditDebitNoteDetail,invoice);
        String path = envProperties.getBasePath() +"credit-note-price-gap" + fileName;

        try (OutputStream outputStream = Files.newOutputStream(Paths.get(path))) {
            HtmlConverter.convertToPdf(invoiceHTMLTemplate, outputStream);
            outputStream.flush();
            File file1 = new File(envProperties.getBasePath() +"credit-note-price-gap" + fileName);
            FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "CREDIT_NOTE/PRICE_GAP", fileName, file1, true);
            DocumentDetailData documentDetailData = saveToDocumentDetail(fileDetail, userId, fileName, InvoiceDocType.SYSTEM_INVOICE, FileType.CREDIT_NOTE, creditNoteNo);
            return documentDetailData;

        }catch (Exception e){
            throw new SumoException("Error making credit note with number :{}",e.getMessage());
        }
    }

    private String generateCreditNotePriceGapTemplate(SalesPerformaInvoiceCreditDebitNoteDetail detail,SalesPerformaInvoice invoice) throws SumoException, TemplateRenderingException, IOException {
        if (Objects.nonNull(detail)) {

            CreditNotePriceGapTemplate creditNotePriceGapTemplate = new CreditNotePriceGapTemplate(detail,props.getBasePath(),
                    NumberToWord.getInstance().convertNumberToWords(detail.getTotalAmount().setScale(0, RoundingMode.UP).intValue()),
                    generateCreditNoteByFinancialYear(FileType.CREDIT_NOTE.value(), 7),masterDataCache.getUnit(invoice.getUnitId()),invoice);
            return creditNotePriceGapTemplate.getContent();
        } else {
            throw new SumoException("Please provide valid invoice id");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CreditDebitNoteDetail getCreditDebitDetail(Integer id) throws Exception {
        SalesPerformaInvoiceCreditDebitNoteDetail detail = invoiceDao.find(SalesPerformaInvoiceCreditDebitNoteDetail.class,id);
        return SCMDataConverter.convert(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public URL downloadCreditNoteDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException {
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        if (performa != null && performa.getCreditNoteDocId() != null) {
            DocumentDetailData document = invoiceDao.find(DocumentDetailData.class, performa.getCreditNoteDocId());
            return fileArchiveService.getSignedUrl(document.getS3Bucket(), document.getS3Key());
        }
        return performa != null ? new URL(performa.getCreditNoteDocUrl()) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public URL downloadDebitNoteDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException {
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        if (performa != null && performa.getDebitNoteDocId() != null) {
            DocumentDetailData document = invoiceDao.find(DocumentDetailData.class, performa.getDebitNoteDocId());
            return fileArchiveService.getSignedUrl(document.getS3Bucket(), document.getS3Key());
        }
        return performa != null ? new URL(performa.getDebitNoteDocUrl()) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, String> generateCreditNote(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException {

        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        SalesPerformaInvoice invoice = SCMDataConverter.convert(performa, masterDataCache, scmCache);

        if(Objects.isNull(performa.getGeneratedCreditNoteId()) && Objects.isNull(performa.getGeneratedDebitNoteId())){
            throw new SumoException("Please upload invoice first");
        }

        Map<String, String> response = new HashMap<>();
        String fileName = "CREDIT_NOTE_" + invoiceId + ".pdf";
        String path = envProperties.getBasePath() + "/b2b-invoice/" + fileName;

        if (performa.getType().equals(SalesPerformaType.B2B_RETURN.value())) {
            String creditNote = performa.getGeneratedCreditNoteId();
            String invoiceHTMLTemplate = generateB2BInvoiceReverseTemplate(invoice,creditNote);
            try (OutputStream outputStream = Files.newOutputStream(Paths.get(path))) {
                HtmlConverter.convertToPdf(invoiceHTMLTemplate, outputStream);
                outputStream.flush();
                File file1 = new File(envProperties.getBasePath() + "/b2b-invoice/" + fileName);
                FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_REQUESTS/CREDIT_NOTE", fileName, file1, true);
                DocumentDetailData documentDetailData = saveToDocumentDetail(fileDetail, userId, fileName, InvoiceDocType.valueOf(performa.getInvoiceType()), FileType.SALES_INVOICE, invoiceId);
                performa.setCreditNoteDocId(documentDetailData.getDocumentId());
                performa.setCreditNoteDocUrl(fileDetail.getUrl());
                performa.setGeneratedCreditNoteId(creditNote);
                invoiceDao.update(performa, true);
                response.put("credit_note", fileDetail.getUrl());
                return response;
            } catch (IOException e) {
                LOG.error("Error when trying to create b2b credit note :::::: {}", invoiceId, e);
                throw new SumoException("Error creating b2b credit note");
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, String> generateCorrectionCreditNote(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException {

        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        SalesPerformaInvoice invoice = SCMDataConverter.convert(performa, masterDataCache, scmCache);
        SalesPerformaInvoiceCorrected correctedPerforma = invoiceDao.getCorrectedInvoiceDetails(invoiceId,SalesPerformaCorrectedType.CREDIT_NOTE.value()).get(0);
        CorrectedSalesInvoiceDetails correctedInvoice = SCMDataConverter.convert(correctedPerforma);

        if(Objects.isNull(correctedInvoice.getIrnNo())){
            throw new SumoException("Please upload excel");
        }

        Map<String, String> response = new HashMap<>();
        String fileName = "CORRECTION_CREDIT_NOTE_" + invoiceId + ".pdf";
        String path = envProperties.getBasePath() + "/b2b-invoice/" + fileName;

        if (performa.getType().equals(SalesPerformaType.B2B_SALES.value())) {
            String invoiceHTMLTemplate = generateCreditCorrectionTemplate(invoice,correctedInvoice,correctedInvoice.getGeneratedCreditNoteId());
            try (OutputStream outputStream = Files.newOutputStream(Paths.get(path))) {
                HtmlConverter.convertToPdf(invoiceHTMLTemplate, outputStream);
                outputStream.flush();
                File file1 = new File(envProperties.getBasePath() + "/b2b-invoice/" + fileName);
                FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_REQUESTS/CREDIT_NOTE", fileName, file1, true);
                DocumentDetailData documentDetailData = saveToDocumentDetail(fileDetail, userId, fileName, InvoiceDocType.valueOf(performa.getInvoiceType()), FileType.SALES_INVOICE, invoiceId);
                correctedPerforma.setCreditNoteDocId(documentDetailData.getDocumentId());
                correctedPerforma.setCreditNoteDocUrl(fileDetail.getUrl());
                correctedPerforma.setGeneratedCreditNoteId(correctedInvoice.getGeneratedCreditNoteId());
                invoiceDao.update(correctedPerforma, true);
                response.put("credit_note", fileDetail.getUrl());
                return response;
            } catch (IOException e) {
                LOG.error("Error when trying to create b2b correction credit note :::::: {}", invoiceId, e);
                throw new SumoException("Error creating b2b correction credit note");
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, String> generateDebitNote(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException {

        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        SalesPerformaInvoice invoice = SCMDataConverter.convert(performa, masterDataCache, scmCache);

        if(Objects.isNull(performa.getGeneratedDebitNoteId()) && Objects.isNull(performa.getGeneratedCreditNoteId())){
            throw new SumoException("Please Upload Invoice first");
        }

        String fileName = "DEBIT_NOTE_" + invoiceId + ".pdf";
        String path = envProperties.getBasePath() + "/b2b-invoice/" + fileName;
        Map<String, String> response = new HashMap<>();

        if (performa.getType().equals(SalesPerformaType.RETURN_TO_VENDOR.value()) &&
                      Objects.isNull(performa.getDebitNoteDocId())) {
            String invoiceHTMLTemplate = generateDebitNoteTemplate(invoice,invoice.getGeneratedDebitNoteId());
            try (OutputStream outputStream = Files.newOutputStream(Paths.get(path))) {
                HtmlConverter.convertToPdf(invoiceHTMLTemplate, outputStream);
                outputStream.flush();
                File file = new File(envProperties.getBasePath() + "/b2b-invoice/" + fileName);
                FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_REQUESTS/DEBIT_NOTE", file, true, 5);
                DocumentDetailData documentDetailData = saveToDocumentDetail(fileDetail, userId, fileName, InvoiceDocType.valueOf(performa.getInvoiceType()), FileType.SALES_INVOICE, invoiceId);
                performa.setDebitNoteDocUrl(fileDetail.getUrl());
                performa.setDebitNoteDocId(documentDetailData.getDocumentId());
                invoiceDao.update(performa, true);

                VendorDetail vendorDetail = scmCache.getVendorDetail(performa.getVendor());
                VendorDebitNoteNotificationTemplate vendorDebitNoteNotificationTemplate = new VendorDebitNoteNotificationTemplate(performa, envProperties.getBasePath(), performa.getVendor(), vendorDetail);
                VendorDebitNoteNotification vendorDebitNoteNotification = new VendorDebitNoteNotification(vendorDebitNoteNotificationTemplate, envProperties.getEnvType(), null);

                List<AttachmentData> attachments = new ArrayList<>();
                AttachmentData debitNote = new AttachmentData(IOUtils.toByteArray(new FileInputStream(file)), "DEBIT_NOTE",
                        "application/pdf");
                attachments.add(debitNote);
                vendorDebitNoteNotification.sendRawMail(attachments);
                response.put("debit_note", fileDetail.getUrl());
                return response;

            } catch (IOException e) {
                LOG.error("Error when trying to create b2b debit note :::::: {}", invoiceId, e);
                throw new SumoException("Error creating b2b debit note");
            } catch (EmailGenerationException e) {
                LOG.error("Error when trying to generate mail for b2b debit note :::::: {}", invoiceId, e);
                throw new SumoException("Error creating mail for b2b debit note");
            }
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, String> generateCorrectionDebitNote(Integer invoiceId, Integer userId) throws SalesPerformaInvoiceException, SumoException, IOException, TemplateRenderingException {

        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        SalesPerformaInvoice invoice = SCMDataConverter.convert(performa, masterDataCache, scmCache);
        SalesPerformaInvoiceCorrected correctedPerforma = invoiceDao.getCorrectedInvoiceDetails(invoiceId,SalesPerformaCorrectedType.DEBIT_NOTE.value()).get(0);
        CorrectedSalesInvoiceDetails correctedInvoice = SCMDataConverter.convert(correctedPerforma);

        Map<String, String> response = new HashMap<>();
        String fileName = "CORRECTION_DEBIT_NOTE_" + invoiceId + ".pdf";
        String path = envProperties.getBasePath() + "/b2b-invoice/" + fileName;

        if(Objects.isNull(correctedInvoice.getIrnNo())){
            throw new SumoException("Please upload excel");
        }

        if (performa.getType().equals(SalesPerformaType.B2B_SALES.value())) {
            String invoiceHTMLTemplate = generateDebitCorrectionTemplate(invoice,correctedInvoice,correctedInvoice.getGeneratedDebitNoteId());
            try (OutputStream outputStream = Files.newOutputStream(Paths.get(path))) {
                HtmlConverter.convertToPdf(invoiceHTMLTemplate, outputStream);
                outputStream.flush();
                File file1 = new File(envProperties.getBasePath() + "/b2b-invoice/" + fileName);
                FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_REQUESTS/DEBIT_NOTE", fileName, file1, true);
                DocumentDetailData documentDetailData = saveToDocumentDetail(fileDetail, userId, fileName, InvoiceDocType.valueOf(performa.getInvoiceType()), FileType.SALES_INVOICE, invoiceId);
                correctedPerforma.setDebitNoteDocId(documentDetailData.getDocumentId());
                correctedPerforma.setDebitNoteDocUrl(fileDetail.getUrl());
                invoiceDao.update(correctedPerforma, true);
                response.put("debit_note", fileDetail.getUrl());
                return response;
            } catch (IOException e) {
                LOG.error("Error when trying to create b2b correction debit note :::::: {}", invoiceId, e);
                throw new SumoException("Error creating b2b correction debit note");
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean uploadCorrectionInvoiceSheet(MultipartFile file, Integer invoiceId, String type) throws IOException, SumoException {

        InputStream excelFile = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(excelFile);
        Sheet dataTypeSheet = workbook.getSheetAt(0);
        Row headerRow = dataTypeSheet.getRow(0);

        for (int i = 1; i <= dataTypeSheet.getLastRowNum(); i++) {
            Row row = dataTypeSheet.getRow(i);
            String docId = row.getCell(4).getStringCellValue();
            SalesPerformaInvoiceCorrected correctedPerforma = new SalesPerformaInvoiceCorrected();
            if(type.equals(SalesPerformaCorrectedType.CREDIT_NOTE.value())){
                correctedPerforma = invoiceDao.findByCreditNoteId(docId);
            }else if(type.equals(SalesPerformaCorrectedType.DEBIT_NOTE.value())){
                correctedPerforma =  invoiceDao.findByDebitNoteId(docId);
            }

            if (Objects.nonNull(correctedPerforma) && correctedPerforma.getId().equals(invoiceId) && correctedPerforma.getType().equals(type)) {
                correctedPerforma.setIrnNo((row.getCell(1).getStringCellValue()));
                correctedPerforma.setUploadedAckNo(row.getCell(2).getStringCellValue());
                correctedPerforma.setSignedQrCode(row.getCell(10).toString());
                correctedPerforma.setUploadedEwayNo(row.getCell(11).getStringCellValue());
                try {
                    QRGenerationServiceImpl impl = new QRGenerationServiceImpl();
                    byte[] generatedBarCode = impl.getQRCodeImage(correctedPerforma.getSignedQrCode(), 100, 100);
                    InputStream inputStream = new ByteArrayInputStream(generatedBarCode);
                    MultipartFile barcodeFile = new MockMultipartFile("new file name", "Original file name", ContentType.IMAGE_PNG.toString(), inputStream);
                    String fileName = "CORRECTION_BARCODE_" + correctedPerforma.getId() + "." + MimeType.PNG.name().toLowerCase();
                    FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "INVOICE_BARCODE/INVOICE", fileName, barcodeFile);
                    DocumentDetail documentDetail = new DocumentDetail();
                    documentDetail.setMimeType(MimeType.PNG);
                    documentDetail.setUploadType(DocUploadType.INVOICE_BARCODE);
                    documentDetail.setFileType(FileType.SALES_INVOICE);
                    documentDetail.setDocumentLink(fileName);
                    documentDetail.setS3Key(s3File.getKey());
                    documentDetail.setFileUrl(s3File.getUrl());
                    documentDetail.setS3Bucket(s3File.getBucket());
                    documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(SCMServiceConstants.SYSTEM_USER, "SYSTEM", "SYSTEM"));
                    documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
                    DocumentDetailData data = invoiceDao.add(SCMDataConverter.convert(documentDetail), true);
                    correctedPerforma.setBarcodeId(data.getDocumentId());
                } catch (Exception e) {
                    LOG.error("Encountered error while uploading document", e);
                }
                invoiceDao.update(correctedPerforma, true);
            }else {
                throw new SumoException("Cannot Find Invoice for the Invoice Id");
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public URL downloadCorrectionCreditNoteDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException {
        SalesPerformaInvoiceCorrected correctedPerforma = invoiceDao.getCorrectedInvoiceDetails(invoiceId,SalesPerformaCorrectedType.CREDIT_NOTE.value()).get(0);
        if (Objects.nonNull(correctedPerforma) && Objects.nonNull(correctedPerforma.getCreditNoteDocId())) {
            DocumentDetailData document = invoiceDao.find(DocumentDetailData.class, correctedPerforma.getCreditNoteDocId());
            return fileArchiveService.getSignedUrl(document.getS3Bucket(), document.getS3Key());
        }
        return Objects.nonNull(correctedPerforma) ? new URL(correctedPerforma.getCreditNoteDocUrl()) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public URL downloadCorrectionDebitNoteDoc(Integer invoiceId) throws SalesPerformaInvoiceException, SumoException, MalformedURLException {
        SalesPerformaInvoiceCorrected correctedPerforma = invoiceDao.getCorrectedInvoiceDetails(invoiceId,SalesPerformaCorrectedType.DEBIT_NOTE.value()).get(0);
        if (Objects.nonNull(correctedPerforma) && Objects.nonNull(correctedPerforma.getDebitNoteDocId())) {
            DocumentDetailData document = invoiceDao.find(DocumentDetailData.class, correctedPerforma.getDebitNoteDocId());
            return fileArchiveService.getSignedUrl(document.getS3Bucket(), document.getS3Key());
        }
        return Objects.nonNull(correctedPerforma) ? new URL(correctedPerforma.getDebitNoteDocUrl()) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean validateAndClose(Integer invoiceId, Integer userId) throws SumoException, SalesPerformaInvoiceException {
        SalesPerformaDetailData performa = invoiceDao.find(SalesPerformaDetailData.class, invoiceId);
        List<SalesPerformaInvoiceCorrected> correctedPerformaList = invoiceDao.getCorrectedInvoiceDetails(invoiceId, null);

        if(Objects.nonNull(correctedPerformaList)){
            for (SalesPerformaInvoiceCorrected correctedPerforma : correctedPerformaList) {
                if (Objects.isNull(correctedPerforma.getIrnNo())) {
                    throw new SumoException("Please upload excel");
                }
                if (correctedPerforma.getType().equals(SalesPerformaCorrectedType.CREDIT_NOTE.value()) && Objects.isNull(correctedPerforma.getCreditNoteDocId())) {
                    throw new SumoException("Please generate credit note");
                }
                if (correctedPerforma.getType().equals(SalesPerformaCorrectedType.DEBIT_NOTE.value()) && Objects.isNull(correctedPerforma.getDebitNoteDocId())) {
                    throw new SumoException("Please generate debit note");
                }
                correctedPerforma.setInvoiceStatus(SalesPerformaStatus.CLOSED_WITH_CORRECTION.value());
                invoiceDao.update(correctedPerforma,false);
            }
        }

        createInvoiceStatusEvent(performa, userId, SalesPerformaStatus.valueOf(performa.getStatus()), SalesPerformaStatus.CLOSED_WITH_CORRECTION);
        performa.setStatus(SalesPerformaStatus.CLOSED_WITH_CORRECTION.value());
        invoiceDao.update(performa,false);

        return true;
    }


}
