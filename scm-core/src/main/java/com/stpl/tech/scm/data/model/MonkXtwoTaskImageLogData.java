package com.stpl.tech.scm.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "MONK_XTWO_TASK_IMAGE_LOG_DATA")
public class MonkXtwoTaskImageLogData implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "TASK_ID")
    private Integer taskId;

    @Column(name = "ORDER_ID")
    private Integer orderId;

    @Column(name = "IS_MANUAL_TASK")
    private String isManualTask;

    @Column(name = "TASK_QUANTITY")
    private Integer taskQuantity;

    @Column(name = "IS_CLUBBED")
    private String isClubbed;

    @Column(name = "IS_SPLIT")
    private String isSplit;

    @Column(name = "LINKED_TASK_ID")
    private Integer linkedTaskId;

    @Column(name = "MONK_NAME")
    private String monkName;

    @Column(name = "CLUBBED_TASK")
    private String clubbedTask;

    @Column(name = "STRATEGY_NAME")
    private String strategyName;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DOCUMENT_ID", referencedColumnName = "DOCUMENT_ID")
    private DocumentDetailData documentDetail;
} 