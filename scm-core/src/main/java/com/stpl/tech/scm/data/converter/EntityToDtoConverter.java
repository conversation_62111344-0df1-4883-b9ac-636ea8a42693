package com.stpl.tech.scm.data.converter;

import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.RegionProductPackagingMappingData;
import com.stpl.tech.scm.domain.model.RegionProductPackagingMapping;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class EntityToDtoConverter {

    public static Map<String, RegionProductPackagingMapping> convertToDto(List<RegionProductPackagingMappingData> regionProductPackagingMappingDataList) {

        Map<String, RegionProductPackagingMapping> resultMap = new HashMap<>();
        for(RegionProductPackagingMappingData data : regionProductPackagingMappingDataList) {
            RegionProductPackagingMapping mapping = new RegionProductPackagingMapping();
            mapping.setId(data.getMappingId());
            mapping.setRegionCode(data.getRegionCode());
            mapping.setProductId(data.getProductId());
            mapping.setPackagingId(data.getPackagingId());
            mapping.setStatus(data.getStatus());
            mapping.setCreatedBy(data.getCreatedBy());

            String KEY = SCMUtil.generateUniqueKey(data.getRegionCode(), data.getProductId().toString());
            resultMap.put(KEY, mapping);
        }

        return resultMap;
    }

}
