package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.data.transport.model.FullfillmentData;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataUnitLevel;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataWarehouseLevel;

import java.util.ArrayList;
import java.util.List;

public interface FullfillmentReportGenerateService {
    void generateWarehouseLevelSheet(ArrayList<FullfillmentDataWarehouseLevel> result , String sheetName, Boolean isNew, String fileName);
    void generateDetailFullFillmentSheet(List<FullfillmentData> result, String fileName, String sheetName);
    void generateUnitLevelDetailFullFillmentSheet(ArrayList<FullfillmentDataUnitLevel> result , String fileName, String sheetName);
}
