package com.stpl.tech.scm.data.converter;

import com.stpl.tech.scm.data.model.CommonLogsData;
import com.stpl.tech.scm.data.model.RegionProductPackagingMappingData;
import com.stpl.tech.scm.domain.model.LogType;
import com.stpl.tech.scm.domain.model.RegionProductPackagingMapping;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.domain.RequestContext;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
public class DtoToEntityConverter {

    public static RegionProductPackagingMappingData convertToEntity(RegionProductPackagingMapping mapping) {
        RegionProductPackagingMappingData regionProductPackagingMappingData = new RegionProductPackagingMappingData();
        regionProductPackagingMappingData.setRegionCode( mapping.getRegionCode() );
        regionProductPackagingMappingData.setProductId( mapping.getProductId() );
        regionProductPackagingMappingData.setPackagingId( mapping.getPackagingId() );
        regionProductPackagingMappingData.setStatus( AppConstants.ACTIVE );
        regionProductPackagingMappingData.setCreatedBy( RequestContext.getContext().getLoggedInUserId() );
        return regionProductPackagingMappingData;
    }

    public static CommonLogsData createEntity(String fromState, String toState, Integer logTypeId, LogType logType, String logMsg) {
        CommonLogsData contractStatusLog = new CommonLogsData();
        contractStatusLog.setFromState(fromState);
        contractStatusLog.setToState(toState);
        Integer updatedBy = RequestContext.getContext().getLoggedInUserId();
        if(Objects.isNull(updatedBy) || updatedBy <= 0) {
            updatedBy = AppConstants.SYSTEM_EMPLOYEE_ID;
        }
        contractStatusLog.setUpdatedBy(updatedBy);
        contractStatusLog.setLogType(logType);
        contractStatusLog.setLogTypeId(logTypeId);
        contractStatusLog.setLogMessage(logMsg);
        return contractStatusLog;
    }

}
