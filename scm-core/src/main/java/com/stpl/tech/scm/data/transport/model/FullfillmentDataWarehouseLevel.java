package com.stpl.tech.scm.data.transport.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FullfillmentDataWarehouseLevel {
    String transferringUnit;

    // Overall
    Double avgFPer;
    Double avgOnTimeFFPer;
    Double avgOnDateFFPer;

    // Impact FF
    Double avgImFPer;
//    Double avgImOnTimeFFPer;
//    Double avgImOnDateFFPer;
    
    // critical 
    Double criticalAvg;
    Double criticalOnTimeFF;
    Double criticalOnDateFF;
    Double criticalProductFF;
    String isCriticalProd;
    
    // baker
    Double bakeryFP;
    Double bakeryOnTimeFP;
    Double bakeryOnDateFP;
    
    // without bakery
    Double withoutBakeryAvgFPer;
    Double withoutBakeryOnTimeAvgFPer;
    Double withoutBakeryOnDateAvgFPer;
    
    // Level 1–5
    Double productLevel1FFPer = 0.0;
    Double productLevel1OnTimeFFPer = 0.0;
    Double productLevel1OnDateFFPer = 0.0;

    Double productLevel2FFPer = 0.0;
    Double productLevel2OnTimeFFPer = 0.0;
    Double productLevel2OnDateFFPer = 0.0;

    Double productLevel3FFPer = 0.0;
    Double productLevel3OnTimeFFPer = 0.0;
    Double productLevel3OnDateFFPer = 0.0;

    Double productLevel4FFPer = 0.0;
    Double productLevel4OnTimeFFPer = 0.0;
    Double productLevel4OnDateFFPer = 0.0;

    Double productLevel5FFPer = 0.0;
    Double productLevel5OnTimeFFPer = 0.0;
    Double productLevel5OnDateFFPer = 0.0;
}
