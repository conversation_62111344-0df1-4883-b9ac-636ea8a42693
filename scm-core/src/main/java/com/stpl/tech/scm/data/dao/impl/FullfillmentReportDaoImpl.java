package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.FullfillmentReportQueries;
import com.stpl.tech.scm.data.dao.FullfillmentReportDao;
import com.stpl.tech.scm.data.transport.model.FullfillmentData;
import com.stpl.tech.scm.data.transport.model.MenuToScmData;
import com.stpl.tech.util.EnvType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Repository
public class FullfillmentReportDaoImpl implements FullfillmentReportDao {

    @Autowired
    FullfillmentReportQueries fullfillmentReportQueries;
    @Autowired
    DataSource dumpDataSource;
    @Autowired
    private EnvProperties env;


    public List<FullfillmentData> getFullfillmentData(int numberOfDays) throws SumoException {
        try {
            log.info("Retrieving Fullfillment Data for last {} days", numberOfDays);
            Connection connection = dumpDataSource.getConnection();
            PreparedStatement statement;
            String defaultOnTimeThreshold = env.getThresholdTimeForFullFillmentReport();
            if (numberOfDays == 1) {
                statement = connection.prepareStatement(fullfillmentReportQueries.getFullfillmentQueryLastDay(EnvType.SPROD));
            } else if (numberOfDays == 30) {
                statement = connection.prepareStatement(fullfillmentReportQueries.getFullfillmentQueryLastThirtyDay(EnvType.SPROD));
            } else if (numberOfDays == 7) {
                statement = connection.prepareStatement(fullfillmentReportQueries.getFullfillmentQueryLastSevenDay(EnvType.SPROD));
            } else {
                return null;
            }
            statement.setString(1, defaultOnTimeThreshold);
//            log.info(statement.toString());
            ResultSet resultSet = statement.executeQuery();
            List<FullfillmentData> result = new ArrayList<>();
            while (resultSet.next()) {
                if (resultSet.getDouble("FINAL_RECEIVED_QUANTITY") != -1) {
                    FullfillmentData fullfillmentData = new FullfillmentData();
                    fullfillmentData.setTransferringUnit(resultSet.getString("TRANSFERRING_UNIT"));
                    fullfillmentData.setTransferringUnitId(resultSet.getLong("TRANSFERRING_UNIT_ID"));
                    fullfillmentData.setRequestingUnit(resultSet.getString("REQUESTING_UNIT"));
                    fullfillmentData.setRequestingUnitId(resultSet.getLong("REQUESTING_UNIT_ID"));
                    fullfillmentData.setRequestOrderId(resultSet.getLong("REQUEST_ORDER_ID"));
                    fullfillmentData.setLastUpdateTime(resultSet.getDate("LAST_UPDATE_TIME"));
                    fullfillmentData.setRequestOrderItemId(resultSet.getLong("REQUEST_ORDER_ITEM_ID"));
                    fullfillmentData.setProductName(resultSet.getString("PRODUCT_NAME"));
                    fullfillmentData.setProductId(resultSet.getLong("PRODUCT_ID"));
                    fullfillmentData.setUnitOfMeasure(resultSet.getString("UNIT_OF_MEASURE"));
                    fullfillmentData.setRequestedAbsoluteQuantity(resultSet.getDouble("REQUESTED_ABSOLUTE_QUANTITY"));
                    fullfillmentData.setTransferredQuantity(resultSet.getDouble("TRANSFERRED_QUANTITY"));
                    fullfillmentData.setReceivedQuantity(resultSet.getDouble("FINAL_RECEIVED_QUANTITY"));
                    fullfillmentData.setIsCritical(resultSet.getString("IS_CRITICAL"));
                    fullfillmentData.setFullfillmentPercentage(resultSet.getDouble("FULLFILLMENT_PERCENTAGE"));
                    fullfillmentData.setImpactedFullfillmentPercentage(Double.valueOf(-1));
                    fullfillmentData.setProductLevel(resultSet.getString("PRODUCT_LEVEL"));

                    //on time and on Date fullfillment Qty's...
                    fullfillmentData.setOnTimeFullFillmentPercentage(resultSet.getDouble("ON_TIME_FULLFILLMENT_PERCENTAGE"));
                    fullfillmentData.setOnDateFullFillmentPercentage(resultSet.getDouble("ON_DATE_FULLFILLMENT_PERCENTAGE"));

                    result.add(fullfillmentData);
                }
            }
            connection.close();
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new SumoException("Error in Fullfillment Report Process ");
        }
    }

    public void getMenuToScmData(Long unitId, HashMap<Long, Set<Long>> scmProductToMenuProduct, HashMap<Long, ArrayList<MenuToScmData>> menuProductToScm) throws SumoException {
        try {
            Connection connection = dumpDataSource.getConnection();
            PreparedStatement statement = connection.prepareStatement(fullfillmentReportQueries.getMenuToScmQuery(EnvType.SPROD));
            statement.setLong(1, unitId);
            ResultSet resultSet = statement.executeQuery();
            List<MenuToScmData> result = new ArrayList<>();

            // product_id -> menu_product_id
            while (resultSet.next()) {
                MenuToScmData menuToScmData = new MenuToScmData();
                menuToScmData.setProductId(resultSet.getLong("PRODUCT_ID"));
                menuToScmData.setRlName(resultSet.getString("RL_NAME"));
                menuToScmData.setRecipeProfile(resultSet.getString("RECIPE_PROFILE"));
                menuToScmData.setScmProductId(resultSet.getLong("SCM_PRODUCT_ID"));
                menuToScmData.setScmProductQuantity(resultSet.getDouble("SCM_PRODUCT_QUANTITY"));
                menuToScmData.setIsCritical(resultSet.getString("IS_CRITICAL"));

                Long scmProductToMenuKey = menuToScmData.getScmProductId();
                Set<Long> menuProductIds = scmProductToMenuProduct.getOrDefault(scmProductToMenuKey, new HashSet<>());
                menuProductIds.add(menuToScmData.getProductId());
                scmProductToMenuProduct.put(scmProductToMenuKey, menuProductIds);

                Long menuProductToScmKey = menuToScmData.getProductId();
                ArrayList<MenuToScmData> menuToScmProductData = menuProductToScm.getOrDefault(menuProductToScmKey, new ArrayList<>());
                menuToScmProductData.add(menuToScmData);
                menuProductToScm.put(menuProductToScmKey, menuToScmProductData);

            }
            connection.close();

        } catch (Exception e) {
            e.printStackTrace();
            throw new SumoException("Error in Fullfillment Report Process ");
        }
    }

}
