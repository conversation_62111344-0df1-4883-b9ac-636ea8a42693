package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.service.MonkXtwoTaskManagementService;
import com.stpl.tech.scm.data.dao.MonkXtwoTaskImageLogDao;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.MonkXtwoTaskImageLogData;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.scm.core.service.EnvProperties;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
public class MonkXtwoTaskManagementServiceImpl implements MonkXtwoTaskManagementService {

    @Autowired
    private FileArchiveService fileArchiveService;
    @Autowired
    private EnvProperties props;
    @Autowired
    private MonkXtwoTaskImageLogDao monkXtwoTaskImageLogDao;
    @Autowired
    private com.stpl.tech.scm.data.dao.SCMAbstractDao documentDetailDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveMonkXtwoTaskImageLog(
            MultipartFile zipFile,
            Integer taskId,
            Integer orderId,
            Integer userId,
            String isManualTask,
            Integer taskQuantity,
            String isClubbed,
            String isSplit,
            Integer linkedTaskId,
            String monkName,
            String clubbedTask,
            String strategyName
    ) {
        if (zipFile == null || zipFile.isEmpty()) {
            throw new IllegalArgumentException("Zip file is required and cannot be empty");
        }
        if (taskId == null || orderId == null || userId == null) {
            throw new IllegalArgumentException("taskId, orderId, and userId are required");
        }
        try {
            FileDetail s3File = uploadZipToS3(zipFile);
            DocumentDetailData doc = createDocumentDetail(s3File, userId);
            MonkXtwoTaskImageLogData log = createTaskImageLog(
                taskId, orderId, isManualTask, taskQuantity, isClubbed, isSplit,
                linkedTaskId, monkName, clubbedTask, strategyName, doc
            );
            monkXtwoTaskImageLogDao.add(log, true);
        } catch (Exception e) {
            throw new RuntimeException("Failed to save MonkXtwoTaskImageLogData: " + e.getMessage(), e);
        }
    }

    private FileDetail uploadZipToS3(MultipartFile zipFile) throws Exception {
        String originalFileName = StringUtils.cleanPath(zipFile.getOriginalFilename());
        String fileName = (originalFileName != null && !originalFileName.isEmpty()) ? originalFileName : ("MONK_XTWO_" + System.currentTimeMillis() + ".zip");
        String dir = SCMServiceConstants.MONK_XTWO_TASK_IMAGE_LOGS_DIR;
        return fileArchiveService.saveFileToS3(props.getS3Bucket(), dir, fileName, zipFile);
    }
    
    @SneakyThrows
    private DocumentDetailData createDocumentDetail(FileDetail s3File, Integer userId) {
        DocumentDetailData doc = new DocumentDetailData();
        doc.setFileType(FileType.ZIP.name());
        doc.setFileUrl(s3File.getUrl());
        doc.setDocumentLink(s3File.getKey().substring(s3File.getKey().lastIndexOf('/') + 1));
        doc.setUpdateTime(new java.util.Date());
        doc.setUpdatedBy(userId);
        doc.setMimeType(MimeType.ZIP.name());
        doc.setDocumentUploadType(DocUploadType.MONK_XTWO_TASK_IMAGE_LOG.name());
        doc.setS3Bucket(s3File.getBucket());
        doc.setS3Key(s3File.getKey());
        documentDetailDao.add(doc, true);
        return doc;
    }

    private MonkXtwoTaskImageLogData createTaskImageLog(
            Integer taskId, Integer orderId, String isManualTask, Integer taskQuantity, String isClubbed, String isSplit,
            Integer linkedTaskId, String monkName, String clubbedTask, String strategyName, DocumentDetailData doc
    ) {
        MonkXtwoTaskImageLogData log = new MonkXtwoTaskImageLogData();
        log.setTaskId(taskId);
        log.setOrderId(orderId);
        log.setIsManualTask(isManualTask);
        log.setTaskQuantity(taskQuantity);
        log.setIsClubbed(isClubbed);
        log.setIsSplit(isSplit);
        log.setLinkedTaskId(linkedTaskId);
        log.setMonkName(monkName);
        log.setClubbedTask(clubbedTask);
        log.setStrategyName(strategyName);
        log.setDocumentDetail(doc);
        return log;
    }
} 